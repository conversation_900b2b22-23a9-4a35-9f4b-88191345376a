import asyncio
import os
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from app.agent.manus import Manus
from app.api.taskchain import router as taskchain_router
from app.api.browser_visualization import router as browser_viz_router
from app.api.plugin_manager import router as plugin_router
from app.api.monitoring import router as monitoring_router
from app.logger import logger

# 创建 FastAPI 应用实例
app = FastAPI(title="OpenManus Pro", description="智能自动化代理平台", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册任务链API路由
app.include_router(taskchain_router)
app.include_router(browser_viz_router)
app.include_router(plugin_router)
app.include_router(monitoring_router)


# 请求模型定义
class ProcessRequest(BaseModel):
    """处理请求的数据模型"""

    prompt: str
    options: dict = {}


class ProcessResponse(BaseModel):
    """处理响应的数据模型"""

    status: str
    result: dict = None
    error: str = None


@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "OpenManus Agent API is running",
        "status": "healthy",
        "version": "1.0.0",
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "openmanus-agent",
        "timestamp": asyncio.get_event_loop().time(),
    }


@app.post("/process", response_model=ProcessResponse)
async def process_request(request: ProcessRequest):
    """处理用户请求的主要端点"""
    try:
        # 验证输入
        if not request.prompt.strip():
            raise HTTPException(status_code=400, detail="Empty prompt provided")

        logger.info(f"Processing request: {request.prompt[:100]}...")

        # 创建并运行 Manus 智能体
        agent = await Manus.create()
        try:
            result = await agent.run(request.prompt)
            logger.info("Request processing completed successfully")
            return ProcessResponse(
                status="success", result={"output": result, "prompt": request.prompt}
            )
        finally:
            # 确保清理资源
            await agent.cleanup()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        return ProcessResponse(status="error", error=str(e))


@app.get("/status")
async def get_status():
    """获取服务状态信息"""
    return {
        "service": "openmanus-agent",
        "status": "running",
        "port": os.getenv("PORT", "8000"),
        "environment": os.getenv("RAILWAY_ENVIRONMENT", "development"),
    }


if __name__ == "__main__":
    # 本地开发时的启动方式
    import uvicorn

    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)
