# Open Manus 智能运营系统（自动化需求模板）

本项目基于 Open Manus 框架，致力于实现“自然语言驱动 + 可视化 + 自动执行”的个人/团队智能运营系统。

## 项目规则

- 所有 Railway 项目描述、需求、部署、集成、踩坑经验，均需归档于 docs/推荐建议日志.md，并保持与自动化需求模板同步。

## 目录结构

- agents/      —— 各类智能体（Agent）模块
- plugins/     —— 插件与第三方集成（Apify、n8n、FlowiseAI等）
- ui/          —— 前端界面与可视化（FlowiseAI + Hyperbrowser MCP）
- config/      —— 配置文件、密钥、MCP等（.env/config/secret.json）
- docs/        —— 需求、功能、接口、集成等文档

## 快速开始

### 🚀 OpenManus Agent (推荐)

OpenManus Agent 是本项目的核心 AI 智能代理服务，基于 FastAPI 构建，**已完成本地部署**。

```bash
# 快速启动 OpenManus Agent
cd agents/openmanus
python -m venv venv-311
source venv-311/bin/activate  # macOS/Linux
pip install --upgrade pip
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8000
```

启动后访问：

- 🏠 服务状态: <http://localhost:8000/status>
- 📚 API 文档: <http://localhost:8000/docs> (Swagger UI)
- ❤️ 健康检查: <http://localhost:8000/health>

**详细部署指南**: [OpenManus 部署指南](docs/openmanus_deployment_guide.md)

### 传统方式启动

1. 安装依赖

   ```bash
   pip install -r requirements.txt
   npm install
   ```

2. 配置环境变量与API Key
   - 复制 .env.example 为 .env
   - 在 config/secret.json 中配置API密钥
3. 一键启动

   ```bash
   docker compose up -d
   # 或
   python main.py
   ```

详细说明见 docs/ 目录下的需求说明书.md、接口文档.md等。

## 部署与集成

### 1. 核心服务启动

1. 启动 FlowiseAI 前端服务

   ```bash
   cd ui/flowise
   docker compose up -d
   ```

   访问: <http://localhost:3010>
   - 默认管理员账号: <<EMAIL>>
   - 默认密码: admin123
   - 首次登录后请立即修改密码

2. 启动 OpenManus API 服务

   ```bash
   python main.py
   ```

   访问: <http://localhost:8000>

### 2. 前端集成

- WebSocket 实时通信已配置
- 浏览器操作可视化通过 Hyperbrowser MCP 实现
- 任务状态实时同步

### 3. 自动化能力

- API 接口: /api/v1/tasks
- 日志查询: /api/v1/logs
- 第三方集成:
  - Apify: 数据采集
  - n8n: 工作流自动化
  - FlowiseAI: 可视化编排

### 4. 监控与维护

- 日志目录: logs/
- 健康检查: /health
- 部署状态: railway_deployment_log.md

详细配置说明见 docs/ 目录下的相关文档。

## 📚 文档导航

### 核心文档

- [需求说明书](docs/需求说明书.md) - 项目需求和功能规划
- [接口文档](docs/接口文档.md) - API接口说明
- [部署指南](docs/deployment.md) - 部署和配置指南

### Git 工作流

- [Git优化指南](docs/git_optimization_guide.md) - 完整的Git配置和工作流程
- [Git快速参考](docs/git_quick_reference.md) - 常用Git命令速查表
- [AI助手Git指南](docs/ai_git_guide.md) - AI助手专用Git操作规范

### 开发指南

- [架构文档](docs/architecture.md) - 系统架构说明
- [集成指南](docs/integration.md) - 第三方服务集成
- [最佳实践](docs/cursor_best_practices.md) - 开发最佳实践

## 🛠️ Git 工作流

本项目已优化Git配置，支持自动化工作流：

```bash
# 检查项目状态
./scripts/git-workflow.sh status

# 提交代码更改
./scripts/git-workflow.sh commit

# 同步上游更新
./scripts/git-workflow.sh sync

# 执行完整工作流
./scripts/git-workflow.sh workflow
```

更多Git操作请参考 [Git快速参考](docs/git_quick_reference.md)。
