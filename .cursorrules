# Manus Pro - Cursor AI 规则配置

## 🎯 项目概述
Manus Pro 是基于 OpenManus 框架的智能 AI 助手平台，集成自然语言对话、浏览器自动化、可视化工作流等功能。

## 🤖 AI 助手行为规范

### 响应风格
- 使用中文进行交流，除非特别要求使用英文
- 保持专业、友好、高效的沟通风格
- 提供具体、可操作的建议和解决方案
- 避免冗长的解释，直接给出关键信息

### 代码生成原则
- 优先使用项目已有的技术栈和架构模式
- 遵循项目的编码规范和最佳实践
- 生成的代码必须包含适当的注释和文档
- 考虑代码的可维护性和可扩展性

### 文档维护
- 代码变更时自动更新相关文档
- 在 `docs/MAINTENANCE_LOG.md` 中记录重要操作
- 确保文档格式符合项目规范
- 验证链接和引用的有效性

## 🏗️ 技术栈偏好

### 后端开发
- **语言**: Python 3.10+
- **框架**: FastAPI / Django
- **数据库**: PostgreSQL / SQLite
- **缓存**: Redis
- **消息队列**: Celery

### 前端开发
- **框架**: React / Next.js
- **样式**: Tailwind CSS
- **状态管理**: React Query / Zustand
- **类型检查**: TypeScript

### 开发工具
- **IDE**: Trae AI / Cursor
- **版本控制**: Git + GitHub
- **容器化**: Docker
- **CI/CD**: GitHub Actions

## 📋 项目管理

### 任务优先级
1. **高优先级**: 核心功能开发、安全问题修复
2. **中优先级**: 性能优化、用户体验改进
3. **低优先级**: 文档完善、代码重构

### 文件组织
- 遵循项目的目录结构规范
- 新文件放在合适的目录中
- 及时清理不再使用的文件
- 重要文件变更需要在 PROJECT_OVERVIEW.md 中记录

## 🔒 安全和合规

### 安全原则
- 敏感信息不能硬编码在代码中
- 使用环境变量管理配置
- 定期检查依赖包的安全漏洞
- 实施适当的访问控制

### 数据保护
- 用户数据加密存储和传输
- 遵循数据隐私法规
- 定期备份重要数据
- 实施数据访问审计

## 🚀 部署和运维

### 部署策略
- 优先使用本地部署方案
- 确保部署过程的可重复性
- 实施蓝绿部署或滚动更新
- 建立完善的监控和告警机制

### 成本控制
- 优先使用开源解决方案
- 合理规划资源使用
- 定期评估和优化成本
- 避免不必要的第三方服务费用

## 📊 质量保证

### 代码质量
- 单元测试覆盖率 > 80%
- 代码必须通过静态分析检查
- 重要功能需要集成测试
- 定期进行代码审查

### 性能要求
- API 响应时间 < 200ms
- 页面加载时间 < 3s
- 系统可用性 > 99.9%
- 支持并发用户数 > 1000

## 🔄 持续改进

### 学习和适应
- 关注最新的技术趋势
- 定期评估和更新技术栈
- 收集用户反馈并持续改进
- 建立知识分享机制

### 创新鼓励
- 鼓励尝试新技术和方法
- 支持实验性项目和原型开发
- 建立失败容忍机制
- 促进团队创新文化

---

> 📝 **配置说明**: 此文件定义了 Cursor AI 在 Manus Pro 项目中的行为规范和偏好设置
> 
> 🔄 **最后更新**: 2025-06-16
> 
> 📋 **维护**: 根据项目发展需要定期更新此配置
