---
description: 开发工具链和代码质量规范
globs: ["**/*.py", "**/*.js", "**/*.ts", "**/*.tsx", "**/*.jsx"]
alwaysApply: false
---

# 开发规范和工具链

## 🔧 开发工具链

### 必需工具
- **IDE**: Trae AI (主要) / Cursor (备用)
- **版本控制**: Git + GitHub
- **包管理**: pip (Python) / npm (Node.js)
- **容器化**: Docker (开发和部署)

### 推荐插件
- **代码格式化**: Black (Python) / Prettier (JavaScript)
- **代码检查**: Flake8 (Python) / ESLint (JavaScript)
- **类型检查**: mypy (Python) / TypeScript

## 🧪 测试与质量保证

### 测试策略
- **单元测试**: 核心业务逻辑覆盖率 > 80%
- **集成测试**: Agent 与外部服务的集成测试
- **端到端测试**: 使用 Playwright 进行浏览器自动化测试
- **性能测试**: 关键接口的响应时间监控

### 质量标准
- **代码审查**: 重要功能必须经过代码审查
- **自动化测试**: CI/CD 流程中集成自动化测试
- **错误监控**: 生产环境错误实时监控和告警

## 🐍 Python 开发规范

### 代码风格
- 使用 Black 进行代码格式化
- 遵循 PEP 8 编码规范
- 使用 type hints 进行类型注解
- 函数和类必须有 docstring

### 项目结构
```
agents/openmanus/
├── src/                    # 源代码
│   ├── __init__.py
│   ├── main.py            # 主入口
│   ├── api/               # API 相关
│   ├── core/              # 核心业务逻辑
│   └── utils/             # 工具函数
├── tests/                 # 测试代码
├── requirements.txt       # 依赖列表
└── pyproject.toml        # 项目配置
```

### 依赖管理
- 使用 `pyproject.toml` 管理依赖
- 生产依赖锁定具体版本
- 开发依赖使用兼容版本范围
- 定期更新依赖包

## 🌐 前端开发规范

### 技术栈
- **框架**: React / Next.js
- **样式**: Tailwind CSS
- **状态管理**: React Query / Zustand
- **类型检查**: TypeScript

### 代码风格
- 使用 Prettier 进行代码格式化
- 遵循 ESLint 规则
- 使用 TypeScript 严格模式
- 组件必须有 PropTypes 或 TypeScript 接口

### 组件规范
```typescript
// 组件文件结构
interface ComponentProps {
  // 属性定义
}

const Component: React.FC<ComponentProps> = ({ props }) => {
  // 组件逻辑
  return (
    // JSX
  );
};

export default Component;
```

## 🔄 Git 工作流规范

### 分支策略
- **main**: 主分支，保持稳定
- **develop**: 开发分支，集成新功能
- **feature/***: 功能分支
- **hotfix/***: 紧急修复分支

### 提交规范
使用 Conventional Commits 格式：
```
type(scope): description

[optional body]

[optional footer]
```

类型说明：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 代码审查
- 所有代码必须通过 Pull Request
- 至少一人审查后才能合并
- 自动化测试必须通过
- 代码覆盖率不能降低

## 🚀 CI/CD 规范

### 自动化流程
1. **代码检查**: 格式化、语法检查、类型检查
2. **自动化测试**: 单元测试、集成测试
3. **安全扫描**: 依赖漏洞扫描
4. **构建验证**: 确保代码可以正常构建
5. **部署**: 自动部署到测试/生产环境

### 部署策略
- **测试环境**: 每次 PR 自动部署
- **生产环境**: main 分支手动触发
- **回滚机制**: 支持快速回滚到上一版本
- **监控告警**: 部署后自动监控关键指标

## 📦 包管理规范

### Python 包管理
```bash
# 安装依赖
pip install -r requirements.txt

# 添加新依赖
pip install package_name
pip freeze > requirements.txt

# 虚拟环境
python -m venv .venv-magentic
source .venv-magentic/bin/activate
```

### Node.js 包管理
```bash
# 安装依赖
npm install

# 添加新依赖
npm install package_name
npm install --save-dev package_name  # 开发依赖

# 更新依赖
npm update
```

## 🔍 代码质量检查

### 自动化检查
```bash
# Python 代码检查
black --check .
flake8 .
mypy .

# JavaScript/TypeScript 检查
npm run lint
npm run type-check
npm run format:check
```

### 性能监控
- 使用 profiler 分析性能瓶颈
- 监控内存使用情况
- 记录关键操作的执行时间
- 定期进行性能测试

@pyproject.toml
@package.json
@.github/workflows/
@tests/
