---
description: 文档维护和管理规范
globs: ["docs/**/*", "*.md"]
alwaysApply: false
---

# 文档维护规范

## 📝 核心文档管理

### 必须维护的文档
- **项目概览**: 保持 `PROJECT_OVERVIEW.md` 实时更新
- **API 文档**: 接口变更时同步更新 `docs/api_reference.md`
- **部署文档**: 记录部署步骤和配置说明 `docs/deployment.md`
- **需求文档**: 维护 `docs/需求说明书.md` 和功能规划
- **配置指南**: 维护 `docs/configuration_guide.md`
- **故障排除**: 维护 `docs/troubleshooting.md`

### Git 工作流文档
- **Git 优化指南**: `docs/git_optimization_guide.md` - 完整配置和流程
- **AI 助手指南**: `docs/ai_git_guide.md` - AI 专用操作规范
- **工作流脚本**: `scripts/git-workflow.sh` - 自动化Git操作

## 📋 文档规范

### 格式标准
- **格式**: 使用 Markdown 格式，遵循统一的文档模板
- **模板**: 使用 `docs/_templates/document_template.md`
- **编码**: UTF-8 编码
- **换行**: 使用 LF (Unix) 换行符

### 更新频率
- **代码变更**: 代码变更时同步更新相关文档
- **版本控制**: 重要文档变更需要通过 Git 提交记录
- **归档管理**: 过时文档移至 `docs/_archive/`，保留历史记录

### 内容要求
- **准确性**: 确保文档内容与实际代码一致
- **完整性**: 覆盖所有重要功能和配置
- **实用性**: 提供可操作的示例和指导
- **时效性**: 定期检查和更新文档内容

## 🤖 AI 助手文档协作

### 主要职责
1. **实时更新**: 代码变更时同步更新相关文档
2. **格式规范**: 确保所有文档遵循统一格式
3. **链接维护**: 检查和修复断开的链接
4. **内容审查**: 验证文档内容的准确性和完整性
5. **归档管理**: 定期归档过时文档
6. **维护记录**: 在 `docs/MAINTENANCE_LOG.md` 中记录所有维护操作

### 操作要求
**每次维护必须记录**:
- 在 `docs/MAINTENANCE_LOG.md` 中详细记录操作
- 更新 `docs/README.md` 中的最近更新信息
- 重大变更需在 `PROJECT_OVERVIEW.md` 任务管理器中记录

### 标准流程
```bash
# 1. 执行维护操作
# 2. 记录到维护日志
echo "### $(date +%Y-%m-%d) - 维护描述" >> docs/MAINTENANCE_LOG.md
# 3. 更新文档目录
# 4. 提交变更
git add docs/ && git commit -m "docs: 文档维护 - 具体描述"
```

## 🛠️ 文档维护工具

### 自动化脚本
- **维护脚本**: `scripts/doc-maintenance.sh` - 自动化文档维护
- **文档模板**: `docs/_templates/document_template.md` - 统一文档格式
- **AI指南**: `docs/ai_documentation_guide.md` - AI助手文档规范
- **目录管理**: 自动生成和更新 `docs/README.md`

### 维护命令
```bash
# 检查文档状态
./scripts/doc-maintenance.sh check

# 完整维护流程
./scripts/doc-maintenance.sh maintain

# 生成统计报告
./scripts/doc-maintenance.sh stats

# 验证链接有效性
./scripts/doc-maintenance.sh validate-links
```

## ✅ 质量检查标准

### 内容质量
- [ ] 信息准确性：与实际代码保持一致
- [ ] 完整性：覆盖所有重要功能点
- [ ] 清晰性：语言简洁明了，逻辑清晰
- [ ] 实用性：提供可操作的示例和指导

### 格式规范
- [ ] 标题层级：正确使用H1-H4标题
- [ ] 代码块：使用正确的语言标识
- [ ] 链接格式：相对路径链接有效
- [ ] 表格格式：对齐和完整性

### 技术准确性
- [ ] 命令可执行：所有命令都经过验证
- [ ] 路径正确：文件路径和目录结构准确
- [ ] 版本兼容：与当前系统版本兼容
- [ ] 依赖明确：列出所有必需依赖

## 📊 监控和报告

### 文档健康度指标
- **更新频率**: 文档更新的及时性
- **链接有效性**: 断开链接的数量
- **格式一致性**: 格式规范遵循度
- **内容准确性**: 与代码的一致性

### 定期报告
```bash
# 生成周报
./scripts/doc-maintenance.sh stats > reports/doc_weekly_$(date +%Y%m%d).md

# 生成月报
git log --since="1 month ago" --grep="docs:" --oneline > reports/doc_monthly_$(date +%Y%m).log
```

@docs/README.md
@docs/MAINTENANCE_LOG.md
@docs/ai_documentation_guide.md
@scripts/doc-maintenance.sh
