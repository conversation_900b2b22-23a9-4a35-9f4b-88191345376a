# Manus Pro 项目规则

## 🎯 项目概述

**Manus Pro** 是基于 OpenManus 框架的智能 AI 助手平台，集成自然语言对话、浏览器自动化、可视化工作流等功能。

## 🏗️ 项目架构规范

### 目录结构原则

- **前端项目**: 主前端 (`frontend/`) + 运营后台 (`ui/flowise/`)
- **后端服务**: OpenManus Agent (`agents/openmanus/`)
- **配置管理**: 按功能分类 (`config/crawler/`, `config/deployment/`, `config/webhook/`)
- **测试组织**: 单元测试 (`tests/unit/`) + 端到端测试 (`tests/e2e/`)
- **归档机制**: 不活跃代码存放在 `archive/` 目录

### 依赖管理策略

- **统一环境**: 使用 `.venv-magentic/` 作为唯一虚拟环境
- **依赖配置**: 所有 Python 依赖统一在根目录 `pyproject.toml` 管理
- **版本控制**: 生产依赖锁定版本，开发依赖使用兼容版本

## 🚀 部署与运维规则

### Railway.com 部署规范

所有需要本地部署的项目都必须在 Railway 平台上执行部署。作为团队的独立全栈工程师，我被授权负责:

- 所有技术实现
- 项目部署
- 执行必要的终端命令
- 完成任务所需的一切技术操作，包括但不限于:
  - 基于 Open Manus 框架的功能开发和集成
  - 新服务的设计、实现和部署
  - 系统架构优化和性能调优
  - 多步骤技术方案的制定与执行
  - 持续跟进项目目标完成情况

### 预算控制

- **月度预算**: $200/月
- **成本优化**: 优先使用开源方案，减少第三方服务费用
- **资源监控**: 定期检查 Railway 资源使用情况

## 🤖 OpenManus 框架集成规范

### 核心功能实现

- **自然语言交互**: 实现类 Manus 的对话体验
- **浏览器自动化**: 集成可视化 AI Agent 操作
- **工作流编辑**: 使用 Flowise 提供可视化编辑能力

### 技术选型原则

- **复杂功能**: 优先考虑开源项目集成
- **简单功能**: 进行定制开发
- **成熟方案**: 优先采用成熟的开源方案和第三方服务
- **快速集成**: 以高效集成替代重复开发
- **可扩展性**: 确保系统架构的可扩展性和维护性

## 📋 项目管理规范

### 任务管理

- **任务追踪**: 使用 `PROJECT_OVERVIEW.md` 中的任务管理器
- **优先级**: 高/中/低 三级优先级管理
- **状态跟踪**: 待办/进行中/已完成/已归档
- **责任分配**: 明确每个任务的负责人

### 文档维护

#### 核心文档管理

- **项目概览**: 保持 `PROJECT_OVERVIEW.md` 实时更新
- **API 文档**: 接口变更时同步更新 `docs/接口文档.md`
- **部署文档**: 记录部署步骤和配置说明 `docs/deployment.md`
- **需求文档**: 维护 `docs/需求说明书.md` 和功能规划

#### Git 工作流文档

- **Git 优化指南**: `docs/git_optimization_guide.md` - 完整配置和流程
- **Git 快速参考**: `docs/git_quick_reference.md` - 常用命令速查
- **AI 助手指南**: `docs/ai_git_guide.md` - AI 专用操作规范
- **工作流脚本**: `scripts/git-workflow.sh` - 自动化Git操作

#### 文档规范

- **格式标准**: 使用 Markdown 格式，遵循统一的文档模板
- **更新频率**: 代码变更时同步更新相关文档
- **版本控制**: 重要文档变更需要通过 Git 提交记录
- **归档管理**: 过时文档移至 `docs/_archive/`，保留历史记录

#### AI 助手文档协作

- **自动更新**: AI 助手负责维护技术文档的时效性
- **格式检查**: 确保文档格式符合项目规范
- **链接验证**: 定期检查文档中的链接有效性
- **多语言支持**: 重要文档提供中英文版本

## 🧪 测试与质量保证

### 测试策略

- **单元测试**: 核心业务逻辑覆盖率 > 80%
- **集成测试**: Agent 与外部服务的集成测试
- **端到端测试**: 使用 Playwright 进行浏览器自动化测试
- **性能测试**: 关键接口的响应时间监控

### 质量标准

- **代码审查**: 重要功能必须经过代码审查
- **自动化测试**: CI/CD 流程中集成自动化测试
- **错误监控**: 生产环境错误实时监控和告警

## 🔧 开发工具链

### 必需工具

- **IDE**: Trae AI (主要) / Cursor (备用)
- **版本控制**: Git + GitHub
- **包管理**: pip (Python) / npm (Node.js)
- **容器化**: Docker (开发和部署)

### 推荐插件

- **代码格式化**: Black (Python) / Prettier (JavaScript)
- **代码检查**: Flake8 (Python) / ESLint (JavaScript)
- **类型检查**: mypy (Python) / TypeScript

## 🔒 安全与合规

### 安全规范

- **密钥管理**: 敏感信息存储在 `config/secrets.env`
- **访问控制**: API 接口实现适当的认证机制
- **数据保护**: 用户数据加密存储和传输
- **依赖安全**: 定期检查依赖包的安全漏洞

### 合规要求

- **开源协议**: 遵循所使用开源项目的许可证
- **数据隐私**: 符合相关数据保护法规
- **代码规范**: 遵循团队制定的编码标准

## 📈 性能优化指导

### 前端优化

- **代码分割**: 实现路由级别的懒加载
- **资源优化**: 图片压缩、静态资源 CDN
- **缓存策略**: 合理配置浏览器和服务器缓存

### 后端优化

- **数据库优化**: 查询优化和索引设计
- **缓存机制**: 热点数据缓存策略
- **异步处理**: 耗时操作使用异步队列

## 🔄 持续改进机制

### 定期评估

- **周度检查**: 项目进度和问题跟踪
- **月度回顾**: 技术债务和性能指标评估
- **季度规划**: 技术栈升级和架构优化计划

### 知识管理

- **经验沉淀**: 重要问题解决方案文档化
- **最佳实践**: 总结和分享开发经验
- **技术调研**: 新技术的评估和试验记录
