#!/bin/bash

# 文档维护自动化脚本
# 使用方法: ./scripts/doc-maintenance.sh [command]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}📝 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查文档状态
check_docs() {
    print_info "检查文档状态..."
    
    # 检查必需文档是否存在
    required_docs=(
        "README.md"
        "PROJECT_OVERVIEW.md"
        "docs/需求说明书.md"
        "docs/接口文档.md"
        "docs/deployment.md"
        "docs/git_optimization_guide.md"
        "docs/git_quick_reference.md"
        "docs/ai_git_guide.md"
    )
    
    missing_docs=()
    for doc in "${required_docs[@]}"; do
        if [ ! -f "$doc" ]; then
            missing_docs+=("$doc")
        fi
    done
    
    if [ ${#missing_docs[@]} -eq 0 ]; then
        print_success "所有必需文档都存在"
    else
        print_warning "缺失以下文档:"
        for doc in "${missing_docs[@]}"; do
            echo "  - $doc"
        done
    fi
    
    # 检查文档更新时间
    print_info "检查文档更新时间..."
    find docs/ -name "*.md" -type f -exec ls -la {} \; | sort -k6,7
}

# 验证文档链接
validate_links() {
    print_info "验证文档链接..."
    
    # 查找所有 Markdown 文件中的链接
    find . -name "*.md" -type f | while read -r file; do
        print_info "检查文件: $file"
        
        # 提取相对路径链接
        grep -oE '\[.*\]\([^)]+\)' "$file" | grep -oE '\([^)]+\)' | tr -d '()' | while read -r link; do
            # 跳过外部链接
            if [[ $link =~ ^https?:// ]]; then
                continue
            fi
            
            # 检查相对路径文件是否存在
            if [[ $link =~ ^[^#].*$ ]] && [ ! -f "$link" ] && [ ! -d "$link" ]; then
                print_warning "断开的链接: $link (在 $file 中)"
            fi
        done
    done
}

# 格式化文档
format_docs() {
    print_info "格式化文档..."
    
    # 检查是否安装了 prettier
    if command -v prettier &> /dev/null; then
        find docs/ -name "*.md" -type f -exec prettier --write {} \;
        print_success "文档格式化完成"
    else
        print_warning "未安装 prettier，跳过格式化"
    fi
}

# 生成文档目录
generate_toc() {
    print_info "生成文档目录..."
    
    cat > docs/README.md << 'EOF'
# 文档目录

## 📚 核心文档

### 项目概述
- [项目概览](../PROJECT_OVERVIEW.md) - 项目整体概况和任务管理
- [需求说明书](需求说明书.md) - 详细需求和功能规划
- [架构文档](architecture.md) - 系统架构设计

### API 和接口
- [接口文档](接口文档.md) - API 接口详细说明
- [集成指南](integration.md) - 第三方服务集成

### 部署和运维
- [部署指南](deployment.md) - 部署步骤和配置
- [Railway 集成](integration_railway.md) - Railway 平台部署

## 🛠️ Git 工作流

### Git 文档
- [Git 优化指南](git_optimization_guide.md) - 完整的 Git 配置和工作流程
- [Git 快速参考](git_quick_reference.md) - 常用 Git 命令速查表
- [AI 助手 Git 指南](ai_git_guide.md) - AI 助手专用 Git 操作规范

### 最佳实践
- [Cursor 最佳实践](cursor_best_practices.md) - 开发工具使用指南
- [日志和调试](logs_and_debugging.md) - 调试和问题排查

## 🤖 AI 和自动化

### 功能文档
- [功能列表](功能列表.md) - 系统功能清单
- [任务管理器](task_manager.md) - 任务管理系统
- [推荐建议日志](推荐建议日志.md) - 优化建议记录

### 对比分析
- [魔术空间 vs OpenManus 对比](魔术空间_vs_OpenManus_对比白皮书.md) - 技术方案对比

## 📋 工作计划

- [后续工作计划](后续工作计划.md) - 项目发展规划

## 📁 归档文档

过时或不再维护的文档存放在 [_archive](_archive/) 目录中。

---

> 📝 **维护说明**: 本目录由文档维护脚本自动生成和更新
> 
> 🔄 **最后更新**: $(date '+%Y-%m-%d %H:%M:%S')
> 
> 🤖 **维护工具**: `scripts/doc-maintenance.sh`
EOF
    
    print_success "文档目录已生成"
}

# 归档过时文档
archive_docs() {
    print_info "归档过时文档..."
    
    # 创建归档目录
    mkdir -p docs/_archive/$(date +%Y-%m)
    
    # 查找超过 6 个月未修改的文档
    find docs/ -name "*.md" -type f -not -path "docs/_archive/*" -mtime +180 | while read -r file; do
        print_warning "发现过时文档: $file"
        echo "是否归档此文档? (y/N)"
        read -r response
        if [[ $response =~ ^[Yy]$ ]]; then
            mv "$file" "docs/_archive/$(date +%Y-%m)/"
            print_success "已归档: $file"
        fi
    done
}

# 更新文档时间戳
update_timestamps() {
    print_info "更新文档时间戳..."
    
    # 在文档末尾添加或更新时间戳
    find docs/ -name "*.md" -type f -not -path "docs/_archive/*" | while read -r file; do
        # 检查是否已有时间戳
        if grep -q "最后更新" "$file"; then
            # 更新现有时间戳
            sed -i.bak "s/最后更新.*$/最后更新**: $(date '+%Y-%m-%d')/" "$file"
            rm -f "${file}.bak"
        else
            # 添加新时间戳
            echo "" >> "$file"
            echo "> 📝 **最后更新**: $(date '+%Y-%m-%d')" >> "$file"
        fi
    done
    
    print_success "时间戳更新完成"
}

# 生成文档统计报告
generate_stats() {
    print_info "生成文档统计报告..."
    
    cat > docs/doc_stats.md << EOF
# 文档统计报告

> 生成时间: $(date '+%Y-%m-%d %H:%M:%S')

## 📊 文档概况

### 文档数量
- **总文档数**: $(find docs/ -name "*.md" -type f | wc -l)
- **活跃文档**: $(find docs/ -name "*.md" -type f -not -path "docs/_archive/*" | wc -l)
- **归档文档**: $(find docs/_archive/ -name "*.md" -type f 2>/dev/null | wc -l || echo 0)

### 文档大小
- **总大小**: $(du -sh docs/ | cut -f1)
- **平均大小**: $(find docs/ -name "*.md" -type f -exec wc -c {} \; | awk '{sum+=\$1; count++} END {if(count>0) printf "%.1f KB", sum/count/1024; else print "0 KB"}')

### 最近更新
$(find docs/ -name "*.md" -type f -not -path "docs/_archive/*" -exec ls -lt {} \; | head -5 | awk '{print "- " \$9 " (" \$6 " " \$7 " " \$8 ")"}')

### 文档类型分布
- **指南类**: $(find docs/ -name "*guide*.md" -o -name "*指南*.md" | wc -l)
- **API文档**: $(find docs/ -name "*api*.md" -o -name "*接口*.md" | wc -l)
- **配置文档**: $(find docs/ -name "*config*.md" -o -name "*配置*.md" | wc -l)
- **其他**: $(find docs/ -name "*.md" -type f -not -name "*guide*" -not -name "*api*" -not -name "*config*" -not -name "*指南*" -not -name "*接口*" -not -name "*配置*" | wc -l)

## 🔗 链接检查

$(./scripts/doc-maintenance.sh validate-links 2>&1 | grep "断开的链接" | head -10 || echo "未发现断开的链接")

---

> 🔄 **自动生成**: 使用 \`./scripts/doc-maintenance.sh stats\` 命令生成
EOF
    
    print_success "统计报告已生成: docs/doc_stats.md"
}

# 显示帮助信息
show_help() {
    echo "文档维护自动化脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 check           - 检查文档状态"
    echo "  $0 validate-links  - 验证文档链接"
    echo "  $0 format          - 格式化文档"
    echo "  $0 toc             - 生成文档目录"
    echo "  $0 archive         - 归档过时文档"
    echo "  $0 timestamps      - 更新文档时间戳"
    echo "  $0 stats           - 生成统计报告"
    echo "  $0 maintain        - 执行完整维护流程"
    echo "  $0 help            - 显示此帮助信息"
}

# 完整维护流程
full_maintenance() {
    print_info "执行完整文档维护流程..."
    
    check_docs
    validate_links
    format_docs
    generate_toc
    update_timestamps
    generate_stats
    
    print_success "文档维护完成！"
}

# 主函数
main() {
    case "${1:-help}" in
        "check")
            check_docs
            ;;
        "validate-links")
            validate_links
            ;;
        "format")
            format_docs
            ;;
        "toc")
            generate_toc
            ;;
        "archive")
            archive_docs
            ;;
        "timestamps")
            update_timestamps
            ;;
        "stats")
            generate_stats
            ;;
        "maintain")
            full_maintenance
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
