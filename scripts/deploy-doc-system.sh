#!/bin/bash

# 文档维护体系快速部署脚本
# 使用方法: ./scripts/deploy-doc-system.sh [target-directory] [project-type]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo "文档维护体系快速部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [target-directory] [project-type]"
    echo ""
    echo "参数:"
    echo "  target-directory  目标项目目录 (默认: ./new-project)"
    echo "  project-type      项目类型 (python|nodejs|go|general, 默认: general)"
    echo ""
    echo "示例:"
    echo "  $0 /path/to/my-project python"
    echo "  $0 ../new-app nodejs"
    echo "  $0 . general  # 在当前目录部署"
    echo ""
    echo "支持的项目类型:"
    echo "  python   - Python项目 (添加black, isort检查)"
    echo "  nodejs   - Node.js项目 (添加npm lint检查)"
    echo "  go       - Go项目 (添加gofmt检查)"
    echo "  general  - 通用项目 (基础功能)"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SOURCE_DIR="$(dirname "$SCRIPT_DIR")"

# 解析参数
TARGET_DIR="${1:-./new-project}"
PROJECT_TYPE="${2:-general}"

# 验证项目类型
case "$PROJECT_TYPE" in
    python|nodejs|go|general)
        ;;
    *)
        print_error "不支持的项目类型: $PROJECT_TYPE"
        show_help
        exit 1
        ;;
esac

# 显示部署信息
print_info "开始部署文档维护体系..."
print_info "目标目录: $TARGET_DIR"
print_info "项目类型: $PROJECT_TYPE"

# 创建目标目录结构
print_info "创建目录结构..."
mkdir -p "$TARGET_DIR"/{scripts,docs/_templates,.githooks}

# 复制核心文件
print_info "复制核心文件..."

# 复制脚本文件
cp "$SOURCE_DIR/scripts/doc-maintenance.sh" "$TARGET_DIR/scripts/"
cp "$SOURCE_DIR/scripts/git-workflow.sh" "$TARGET_DIR/scripts/"

# 复制文档模板
cp -r "$SOURCE_DIR/docs/_templates/" "$TARGET_DIR/docs/"

# 复制Git配置
cp -r "$SOURCE_DIR/.githooks/" "$TARGET_DIR/"
cp "$SOURCE_DIR/.gitmessage" "$TARGET_DIR/"

# 复制AI指南
cp "$SOURCE_DIR/docs/ai_documentation_guide.md" "$TARGET_DIR/docs/"
cp "$SOURCE_DIR/docs/ai_git_guide.md" "$TARGET_DIR/docs/"
cp "$SOURCE_DIR/docs/git_optimization_guide.md" "$TARGET_DIR/docs/"
cp "$SOURCE_DIR/docs/git_quick_reference.md" "$TARGET_DIR/docs/"

# 设置执行权限
print_info "设置执行权限..."
chmod +x "$TARGET_DIR/scripts/"*.sh
chmod +x "$TARGET_DIR/.githooks/"*

# 根据项目类型定制配置
print_info "根据项目类型定制配置..."

case "$PROJECT_TYPE" in
    python)
        # 修改文档维护脚本，添加Python特定检查
        cat >> "$TARGET_DIR/scripts/doc-maintenance.sh" << 'EOF'

# Python项目特定检查
check_python_format() {
    if command -v black &> /dev/null; then
        print_info "检查Python代码格式..."
        black --check --diff . || {
            print_warning "Python代码格式不符合规范，请运行: black ."
        }
    fi
    
    if command -v isort &> /dev/null; then
        print_info "检查Python导入排序..."
        isort --check-only --diff . || {
            print_warning "Python导入排序不正确，请运行: isort ."
        }
    fi
}

# 在主函数中添加Python检查
if [ "$1" = "check" ] || [ "$1" = "maintain" ]; then
    check_python_format
fi
EOF
        
        # 创建Python特定的必需文档列表
        sed -i.bak 's/required_docs=(/required_docs=(\
        "README.md"\
        "requirements.txt"\
        "setup.py"\
        "docs\/api.md"\
        "docs\/installation.md"/' "$TARGET_DIR/scripts/doc-maintenance.sh"
        rm -f "$TARGET_DIR/scripts/doc-maintenance.sh.bak"
        ;;
        
    nodejs)
        # 修改文档维护脚本，添加Node.js特定检查
        cat >> "$TARGET_DIR/scripts/doc-maintenance.sh" << 'EOF'

# Node.js项目特定检查
check_nodejs_format() {
    if [ -f "package.json" ]; then
        print_info "检查Node.js项目..."
        if npm run lint &> /dev/null; then
            print_success "代码检查通过"
        else
            print_warning "代码检查失败，请运行: npm run lint"
        fi
    fi
}

# 在主函数中添加Node.js检查
if [ "$1" = "check" ] || [ "$1" = "maintain" ]; then
    check_nodejs_format
fi
EOF
        
        # 创建Node.js特定的必需文档列表
        sed -i.bak 's/required_docs=(/required_docs=(\
        "README.md"\
        "package.json"\
        "docs\/api.md"\
        "docs\/setup.md"/' "$TARGET_DIR/scripts/doc-maintenance.sh"
        rm -f "$TARGET_DIR/scripts/doc-maintenance.sh.bak"
        ;;
        
    go)
        # 修改文档维护脚本，添加Go特定检查
        cat >> "$TARGET_DIR/scripts/doc-maintenance.sh" << 'EOF'

# Go项目特定检查
check_go_format() {
    if command -v gofmt &> /dev/null; then
        print_info "检查Go代码格式..."
        unformatted=$(gofmt -l . | grep -v vendor/ | head -10)
        if [ -n "$unformatted" ]; then
            print_warning "以下文件格式不正确:"
            echo "$unformatted"
            print_warning "请运行: gofmt -w ."
        else
            print_success "Go代码格式正确"
        fi
    fi
}

# 在主函数中添加Go检查
if [ "$1" = "check" ] || [ "$1" = "maintain" ]; then
    check_go_format
fi
EOF
        
        # 创建Go特定的必需文档列表
        sed -i.bak 's/required_docs=(/required_docs=(\
        "README.md"\
        "go.mod"\
        "docs\/api.md"\
        "docs\/installation.md"/' "$TARGET_DIR/scripts/doc-maintenance.sh"
        rm -f "$TARGET_DIR/scripts/doc-maintenance.sh.bak"
        ;;
esac

# 创建项目特定的README
print_info "创建项目README..."
cat > "$TARGET_DIR/README.md" << EOF
# 项目名称

> 项目简短描述

## 📚 文档维护

本项目使用自动化文档维护工具：

\`\`\`bash
# 检查文档状态
./scripts/doc-maintenance.sh check

# 完整维护流程
./scripts/doc-maintenance.sh maintain

# Git工作流
./scripts/git-workflow.sh workflow
\`\`\`

## 🛠️ 快速开始

### 安装依赖
\`\`\`bash
# 根据项目类型添加安装命令
EOF

case "$PROJECT_TYPE" in
    python)
        cat >> "$TARGET_DIR/README.md" << 'EOF'
pip install -r requirements.txt
EOF
        ;;
    nodejs)
        cat >> "$TARGET_DIR/README.md" << 'EOF'
npm install
EOF
        ;;
    go)
        cat >> "$TARGET_DIR/README.md" << 'EOF'
go mod download
EOF
        ;;
esac

cat >> "$TARGET_DIR/README.md" << 'EOF'
```

### 配置Git
```bash
# 配置Git hooks和提交模板
git config core.hooksPath .githooks
git config commit.template .gitmessage
```

## 📖 文档

- [文档维护指南](docs/ai_documentation_guide.md)
- [Git工作流指南](docs/git_optimization_guide.md)
- [快速参考](docs/git_quick_reference.md)

## 🤝 贡献

请阅读 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

---

> 📝 **文档维护**: 本项目使用自动化文档维护工具，请定期运行 `./scripts/doc-maintenance.sh maintain`
EOF

# 如果目标目录是Git仓库，配置Git
if [ -d "$TARGET_DIR/.git" ]; then
    print_info "配置Git..."
    cd "$TARGET_DIR"
    git config core.hooksPath .githooks
    git config commit.template .gitmessage
    cd - > /dev/null
else
    print_warning "目标目录不是Git仓库，请手动运行以下命令配置Git:"
    echo "  cd $TARGET_DIR"
    echo "  git init"
    echo "  git config core.hooksPath .githooks"
    echo "  git config commit.template .gitmessage"
fi

# 运行初始检查
print_info "运行初始检查..."
cd "$TARGET_DIR"
./scripts/doc-maintenance.sh toc
./scripts/doc-maintenance.sh check
cd - > /dev/null

print_success "文档维护体系部署完成！"
print_info "部署位置: $TARGET_DIR"
print_info "项目类型: $PROJECT_TYPE"

echo ""
print_info "下一步操作:"
echo "1. cd $TARGET_DIR"
echo "2. 编辑 README.md 添加项目信息"
echo "3. 运行 ./scripts/doc-maintenance.sh maintain"
echo "4. 运行 ./scripts/git-workflow.sh status"

echo ""
print_info "更多信息请查看:"
echo "- docs/ai_documentation_guide.md"
echo "- docs/git_optimization_guide.md"
echo "- docs/reusability_guide.md"
