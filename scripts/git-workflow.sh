#!/bin/bash

# Git工作流优化脚本
# 使用方法: ./scripts/git-workflow.sh [command]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查Git状态
check_status() {
    print_info "检查Git状态..."
    git status --porcelain
    
    if [ -n "$(git status --porcelain)" ]; then
        print_warning "工作区有未提交的更改"
        git status
    else
        print_success "工作区干净"
    fi
}

# 同步上游更新
sync_upstream() {
    print_info "同步上游更新..."
    
    # 获取上游更新
    git fetch upstream
    
    # 检查是否有冲突
    if git merge-base --is-ancestor HEAD upstream/main; then
        print_info "合并上游更新..."
        git merge upstream/main
        print_success "上游更新已合并"
    else
        print_warning "检测到可能的冲突，建议手动处理"
        git log --oneline HEAD..upstream/main
    fi
}

# 清理工作区
cleanup() {
    print_info "清理工作区..."
    
    # 删除已合并的分支
    git branch --merged | grep -v "\*\|main\|master" | xargs -n 1 git branch -d 2>/dev/null || true
    
    # 清理远程跟踪分支
    git remote prune origin
    git remote prune upstream
    
    # 垃圾回收
    git gc --prune=now
    
    print_success "工作区清理完成"
}

# 提交当前更改
commit_changes() {
    if [ -z "$(git status --porcelain)" ]; then
        print_info "没有需要提交的更改"
        return
    fi
    
    print_info "准备提交更改..."
    
    # 显示更改
    git diff --name-status
    
    # 添加所有更改
    git add .
    
    # 提示输入提交信息
    echo "请输入提交信息:"
    read -r commit_message
    
    if [ -n "$commit_message" ]; then
        git commit -m "$commit_message"
        print_success "更改已提交: $commit_message"
    else
        print_error "提交信息不能为空"
        exit 1
    fi
}

# 推送到远程
push_changes() {
    current_branch=$(git branch --show-current)
    print_info "推送分支 $current_branch 到远程..."
    
    git push origin "$current_branch"
    print_success "推送完成"
}

# 创建新功能分支
create_feature_branch() {
    if [ -z "$1" ]; then
        echo "请提供分支名称"
        echo "使用方法: $0 feature <branch-name>"
        exit 1
    fi
    
    branch_name="feature/$1"
    print_info "创建功能分支: $branch_name"
    
    # 确保在main分支
    git checkout main
    
    # 同步最新更改
    sync_upstream
    
    # 创建新分支
    git checkout -b "$branch_name"
    
    print_success "功能分支 $branch_name 已创建"
}

# 显示帮助信息
show_help() {
    echo "Git工作流优化脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 status          - 检查Git状态"
    echo "  $0 sync            - 同步上游更新"
    echo "  $0 cleanup         - 清理工作区"
    echo "  $0 commit          - 提交当前更改"
    echo "  $0 push            - 推送到远程"
    echo "  $0 feature <name>  - 创建新功能分支"
    echo "  $0 workflow        - 执行完整工作流"
    echo "  $0 help            - 显示此帮助信息"
}

# 完整工作流
full_workflow() {
    print_info "执行完整Git工作流..."
    
    check_status
    
    if [ -n "$(git status --porcelain)" ]; then
        commit_changes
    fi
    
    sync_upstream
    push_changes
    cleanup
    
    print_success "工作流执行完成！"
}

# 主函数
main() {
    case "${1:-help}" in
        "status")
            check_status
            ;;
        "sync")
            sync_upstream
            ;;
        "cleanup")
            cleanup
            ;;
        "commit")
            commit_changes
            ;;
        "push")
            push_changes
            ;;
        "feature")
            create_feature_branch "$2"
            ;;
        "workflow")
            full_workflow
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
