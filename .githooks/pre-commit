#!/bin/bash

# Pre-commit hook for code quality checks

set -e

echo "🔍 运行pre-commit检查..."

# 检查Python代码格式
if command -v black &> /dev/null; then
    echo "📝 检查Python代码格式..."
    black --check --diff . || {
        echo "❌ Python代码格式不符合规范，请运行: black ."
        exit 1
    }
fi

# 检查Python导入排序
if command -v isort &> /dev/null; then
    echo "📦 检查Python导入排序..."
    isort --check-only --diff . || {
        echo "❌ Python导入排序不正确，请运行: isort ."
        exit 1
    }
fi

# 检查TypeScript/JavaScript代码
if command -v npm &> /dev/null && [ -f "frontend/package.json" ]; then
    echo "🔧 检查前端代码..."
    cd frontend
    npm run lint || {
        echo "❌ 前端代码检查失败"
        exit 1
    }
    cd ..
fi

# 检查大文件
echo "📏 检查大文件..."
large_files=$(find . -type f -size +10M -not -path "./.git/*" -not -path "./.venv*/*" -not -path "./node_modules/*")
if [ -n "$large_files" ]; then
    echo "❌ 发现大文件 (>10MB):"
    echo "$large_files"
    echo "请考虑使用Git LFS或移除这些文件"
    exit 1
fi

# 检查敏感信息
echo "🔒 检查敏感信息..."
if grep -r -i --exclude-dir=.git --exclude-dir=node_modules --exclude-dir=.venv "password\|secret\|key\|token" . | grep -v ".gitignore" | grep -v "pre-commit" | head -5; then
    echo "⚠️  发现可能的敏感信息，请检查上述文件"
    echo "如果确认无误，请忽略此警告"
fi

echo "✅ Pre-commit检查通过！"
