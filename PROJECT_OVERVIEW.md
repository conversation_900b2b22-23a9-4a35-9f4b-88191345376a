# 📂 Open Manus Pro — 项目总览

## A. 文件夹结构

```
manus pro/
├── .cursor/                   # Cursor IDE 配置
├── .github/                   # GitHub Actions 工作流
├── .gitignore
├── .python-version
├── .trae/                     # Trae AI 配置
├── .venv-magentic/            # Python 虚拟环境（统一管理）
├── agents/                    # AI Agent 相关代码
│   └── openmanus/             # OpenManus Agent 实现
├── archive/                   # 归档目录
│   ├── magentic-ui-fresh/     # 归档的前端项目
│   ├── samples/               # 示例文件
│   ├── recordings/            # 录制文件
│   └── screenshots/           # 截图文件
├── config/                    # 配置文件（按类型组织）
│   ├── crawler/               # 爬虫相关配置
│   ├── deployment/            # 部署相关配置
│   ├── webhook/               # Webhook 配置
│   └── secrets.env            # 环境变量
├── docs/                      # 项目文档
│   └── _archive/              # 归档文档
├── frontend/                  # 主前端项目（Magentic-UI）
├── plugins/                   # 插件目录
├── pyproject.toml            # Python 项目配置（合并所有依赖）
├── src/                      # 源代码
├── tests/                    # 测试文件
│   ├── unit/                 # 单元测试
│   ├── e2e/                  # 端到端测试
│   │   └── magentic-ui-browser-docker/
│   └── README.md             # 测试说明
├── ui/                       # UI 相关
│   └── flowise/              # Flowise 工作流编辑器
│       └── packages/ui/      # Flowise UI 组件
└── uv.lock                   # UV 锁定文件
```

## B. 前端子项目

| 编号 | 目录 | 技术栈 | 说明 | 状态 |
|------|------|--------|------|------|
| 1 | `frontend/` | Gatsby + React + TailwindCSS + Ant Design | 主 UI - Magentic-UI 核心界面 | ✅ 活跃开发 |
| 2 | `ui/flowise/packages/ui/` | React + Material-UI + Redux | Flowise AI 用户界面，用于内部运营和数据回放 | ✅ 保留用于后台管理 |
| 3 | `archive/magentic-ui-fresh/python/packages/autogen-studio/frontend/` | Gatsby + React + TailwindCSS | AutoGen Studio 前端界面（早期试验项目） | 📦 已归档 |

**注意**:

- `agents/openmanus/app/tool/chart_visualization/` 为图表可视化工具（TypeScript + VChart），属于后端工具组件，不是独立前端项目。
- AutoGen Studio 前端已归档，避免维护三套 React 项目，功能与主站重复。

## C. 关键文档引用

| 文档类型 | 文件路径 | 说明 |
|----------|----------|------|
| 接口文档 | [`docs/接口文档.md`](docs/接口文档.md) | API 接口规范 |
| 架构图 | [`docs/architecture.md`](docs/architecture.md) | 系统架构设计 |
| 部署指南 | [`docs/deployment.md`](docs/deployment.md) | 部署配置说明 |
| 集成文档 | [`docs/integration.md`](docs/integration.md) | 第三方集成指南 |
| 需求文档 | [`docs/需求说明书.md`](docs/需求说明书.md) | 项目需求规范 |
| 功能列表 | [`docs/功能列表.md`](docs/功能列表.md) | 功能模块清单 |
| Railway 部署 | [`docs/railway_deployment_log.md`](docs/railway_deployment_log.md) | Railway 平台部署日志 |
| Flowise 集成 | [`docs/flowiseai_frontend_integration.md`](docs/flowiseai_frontend_integration.md) | Flowise 前端集成文档 |

## D. 📋 Open Manus Pro 项目任务管理器（Task Manager）

### 任务管理说明

本任务管理器用于自动化规划、跟踪和归档项目完善过程，支持团队协作与进度可追溯。

| 编号 | 任务内容                         | 优先级 | 负责人 | 状态   | 备注/执行日志         |
|------|----------------------------------|--------|--------|--------|----------------------|
| 1    | plugins/ 插件目录与文档补全      | 高     | AI     | 待办   |                      |
| 2    | 实现 WebSocket 实时通信           | 高     | AI     | 待办   |                      |
| 3    | 前端可视化回放与集成             | 高     | AI     | 待办   |                      |
| 4    | 完善 CI/CD 流程与部署文档        | 中     | AI     | 待办   |                      |
| 5    | 补充详细 API/任务流/前端文档     | 中     | AI     | 待办   |                      |
| 6    | 日志采集、监控与告警机制完善     | 中     | AI     | 待办   |                      |
| 7    | 安全与权限管理机制补充           | 低     | AI     | 待办   |                      |
| 8    | 多语言/国际化支持完善            | 低     | AI     | 待办   |                      |
| 9    | 团队知识归档与最佳实践整理       | 低     | AI     | 待办   |                      |
| 10   | 扫描 & 文档刷新                  | 高     | AI     | 完成   | 自动刷新 PROJECT_OVERVIEW.md，更新文件夹树 & 前端列表（2025-01-15） |
| 11   | 目录重构 & 文档同步              | 高     | AI     | 完成   | 移动浏览器自动化目录，删除空目录，同步文档结构（2025-01-15） |
| 12   | 项目结构优化：合并虚拟环境和依赖管理 | 高     | 系统架构师 | ✅ 已完成 | 删除重复的 `agents/openmanus/venv-311/`，将依赖合并到根目录 `pyproject.toml` |
| 13   | 归档不活跃项目：减少维护负担     | 中     | 系统架构师 | ✅ 已完成 | 创建 `archive/` 目录，归档 `magentic-ui-fresh`、`samples`、`recordings`、`screenshots` |
| 14   | 配置文件重组：按类型分类管理     | 中     | 系统架构师 | ✅ 已完成 | 在 `config/` 下创建 `crawler/`、`deployment/`、`webhook/` 子目录，分类存放配置 |
| 15   | 文档整理：归档重复文档           | 低     | 系统架构师 | ✅ 已完成 | 创建 `docs/_archive/`，归档重复的部署和集成文档 |
| 16   | 测试目录完善：添加单元测试支持   | 中     | 系统架构师 | ✅ 已完成 | 创建 `tests/unit/` 和 `tests/README.md`，完善测试说明文档 |
| 17   | 文档体系重构优化                 | 高     | AI助手     | ✅ 已完成 | 删除5个重复文档，新增3个核心文档，优化文档结构，详见 `docs/MAINTENANCE_LOG.md` (2025-06-16) |
| 18   | 维护脚本更新同步                 | 中     | AI助手     | ✅ 已完成 | 更新 `scripts/doc-maintenance.sh`，同步必需文档列表，完善自动化功能 (2025-06-16) |
| 19   | Cursor规则标准化迁移             | 高     | AI助手     | ✅ 已完成 | 从 `.trae/rules/` 迁移到 `.cursor/rules/`，采用MDC格式，实现智能规则应用 (2025-06-16) |

### 用法

- 每完成/推进一项任务，更新"状态"和"执行日志"列
- 可按实际分配负责人
- 支持细分子任务、添加新任务、归档已完成任务
- 可定期导出/同步到团队协作平台

---

*最后更新: 2025-01-15*
*生成工具: Open Manus Pro AI Assistant*
