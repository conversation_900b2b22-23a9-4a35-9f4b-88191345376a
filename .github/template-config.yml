# GitHub模板仓库配置
# 用于创建文档维护体系模板

name: "文档维护体系模板"
description: "包含自动化文档维护工具的项目模板"
topics:
  - documentation
  - automation
  - git-workflow
  - ai-friendly
  - template

# 模板仓库设置
template:
  name: "doc-maintenance-template"
  description: "自动化文档维护体系模板"
  
# 包含的文件
include:
  - "scripts/"
  - "docs/_templates/"
  - "docs/ai_documentation_guide.md"
  - "docs/ai_git_guide.md"
  - "docs/git_optimization_guide.md"
  - "docs/git_quick_reference.md"
  - "docs/reusability_guide.md"
  - ".githooks/"
  - ".gitmessage"
  - ".github/template-config.yml"

# 排除的文件
exclude:
  - "docs/README.md"
  - "docs/doc_stats.md"
  - "docs/_archive/"
  - "agents/"
  - "ui/"
  - "frontend/"
  - "src/"
  - ".venv*/"
  - "node_modules/"
