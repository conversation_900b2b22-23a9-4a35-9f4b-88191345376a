# <类型>(<范围>): <主题>
#
# <详细描述>
#
# <相关Issue>

# 类型说明:
# feat:     新功能
# fix:      修复bug
# docs:     文档更新
# style:    代码格式调整
# refactor: 重构代码
# test:     测试相关
# chore:    构建过程或辅助工具的变动
# perf:     性能优化
# ci:       CI/CD相关
# build:    构建系统或外部依赖的变动

# 范围说明:
# frontend: 前端相关
# backend:  后端相关
# api:      API相关
# ui:       用户界面
# docs:     文档
# config:   配置文件
# deps:     依赖管理

# 示例:
# feat(frontend): 添加用户登录功能
# 
# - 实现用户名密码登录
# - 添加记住登录状态功能
# - 集成OAuth第三方登录
# 
# Closes #123
