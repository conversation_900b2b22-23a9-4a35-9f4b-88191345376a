{"name": "applescript-mcp", "version": "1.0.4", "description": "AppleScript MCP Framework", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node --esm src/index.ts"}, "keywords": ["mcp", "applescript", "macos"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.1.1"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "ts-node": "^10.9.0"}}