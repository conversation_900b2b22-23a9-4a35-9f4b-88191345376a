# AI助手文档维护指南

> 🤖 专为AI助手设计的文档创建、更新和维护规范

## 📋 目录

- [AI助手职责](#ai助手职责)
- [文档创建规范](#文档创建规范)
- [文档更新流程](#文档更新流程)
- [质量检查标准](#质量检查标准)
- [自动化工具](#自动化工具)
- [协作规范](#协作规范)

## 🎯 AI助手职责

### 主要责任
1. **实时更新**: 代码变更时同步更新相关文档
2. **格式规范**: 确保所有文档遵循统一格式
3. **链接维护**: 检查和修复断开的链接
4. **内容审查**: 验证文档内容的准确性和完整性
5. **归档管理**: 定期归档过时文档

### 权限范围
- ✅ 创建和更新技术文档
- ✅ 修复格式和链接问题
- ✅ 生成文档目录和统计
- ✅ 归档过时文档
- ❌ 删除重要文档
- ❌ 修改项目核心规范

## 📝 文档创建规范

### 使用模板
所有新文档必须基于 `docs/_templates/document_template.md` 创建：

```bash
# 复制模板创建新文档
cp docs/_templates/document_template.md docs/new_document.md
```

### 命名规范
- **中文文档**: 使用中文名称，如 `需求说明书.md`
- **英文文档**: 使用小写字母和下划线，如 `api_reference.md`
- **Git相关**: 以 `git_` 开头，如 `git_optimization_guide.md`
- **AI相关**: 以 `ai_` 开头，如 `ai_documentation_guide.md`

### 文档结构
```markdown
# 标题 (H1 - 只有一个)
> 简短描述

## 📋 目录 (H2)
## 🎯 概述 (H2)
## 📚 主要内容 (H2)
### 子章节 (H3)
#### 详细说明 (H4)
```

### 必需元素
- 📝 文档描述
- 📋 目录结构
- 🎯 概述说明
- 💡 示例代码
- 🔗 相关链接
- 📊 文档信息

## 🔄 文档更新流程

### 1. 检测更新需求
```bash
# AI检查代码变更
git diff --name-only HEAD~1 HEAD

# 识别需要更新的文档
./scripts/doc-maintenance.sh check
```

### 2. 更新文档内容
- 根据代码变更更新相关文档
- 确保示例代码与实际代码一致
- 更新API接口文档
- 修正过时的配置说明

### 3. 格式检查
```bash
# 格式化文档
./scripts/doc-maintenance.sh format

# 验证链接
./scripts/doc-maintenance.sh validate-links
```

### 4. 提交更新
```bash
# 使用规范的提交信息
git add docs/
git commit -m "ai-docs: 更新文档以反映代码变更

- 更新API接口文档
- 修正配置示例
- 添加新功能说明

AI-Generated: Augment Agent"
```

## ✅ 质量检查标准

### 内容质量
- [ ] 信息准确性：与实际代码保持一致
- [ ] 完整性：覆盖所有重要功能点
- [ ] 清晰性：语言简洁明了，逻辑清晰
- [ ] 实用性：提供可操作的示例和指导

### 格式规范
- [ ] 标题层级：正确使用H1-H4标题
- [ ] 代码块：使用正确的语言标识
- [ ] 链接格式：相对路径链接有效
- [ ] 表格格式：对齐和完整性

### 技术准确性
- [ ] 命令可执行：所有命令都经过验证
- [ ] 路径正确：文件路径和目录结构准确
- [ ] 版本兼容：与当前系统版本兼容
- [ ] 依赖明确：列出所有必需依赖

## 🛠️ 自动化工具

### 文档维护脚本
```bash
# 完整维护流程
./scripts/doc-maintenance.sh maintain

# 单独功能
./scripts/doc-maintenance.sh check          # 检查状态
./scripts/doc-maintenance.sh validate-links # 验证链接
./scripts/doc-maintenance.sh format         # 格式化
./scripts/doc-maintenance.sh toc            # 生成目录
./scripts/doc-maintenance.sh stats          # 统计报告
```

### Git工作流集成
```bash
# 文档更新工作流
./scripts/git-workflow.sh status
# 更新文档
./scripts/doc-maintenance.sh maintain
./scripts/git-workflow.sh commit
```

### 自动化检查
- **Pre-commit Hook**: 提交前检查文档格式
- **CI/CD集成**: 自动验证文档链接
- **定期任务**: 每周生成文档统计报告

## 🤝 协作规范

### 与人类协作
```markdown
🤖 AI助手文档更新报告:

更新类型: [新增/修改/删除/归档]
影响文档: 
- docs/api_reference.md
- docs/deployment.md

主要变更:
- 添加新API端点文档
- 更新部署配置说明
- 修正示例代码错误

需要人类确认: [是/否]
原因: [说明原因]
```

### 冲突处理
1. **检测冲突**: 识别文档更新冲突
2. **分析差异**: 比较不同版本的差异
3. **提出建议**: 给出合并建议
4. **等待确认**: 重要变更需要人类确认

### 反馈机制
- 收集用户对文档质量的反馈
- 分析文档使用情况和热点
- 根据反馈优化文档结构
- 持续改进文档维护流程

## 📊 监控和报告

### 文档健康度指标
- **更新频率**: 文档更新的及时性
- **链接有效性**: 断开链接的数量
- **格式一致性**: 格式规范遵循度
- **内容准确性**: 与代码的一致性

### 定期报告
```bash
# 生成周报
./scripts/doc-maintenance.sh stats > reports/doc_weekly_$(date +%Y%m%d).md

# 生成月报
./scripts/doc-maintenance.sh maintain
git log --since="1 month ago" --grep="ai-docs" --oneline > reports/doc_monthly_$(date +%Y%m).log
```

### 改进建议
- 基于使用数据优化文档结构
- 识别文档盲点和缺失
- 提出新文档创建建议
- 优化自动化工具效率

---

> 📝 **文档信息**
> 
> - **创建日期**: 2025-06-16
> - **最后更新**: 2025-06-16
> - **维护者**: AI助手团队
> - **文档版本**: v1.0
> - **适用范围**: 所有AI助手
> 
> 🔄 **更新说明**: 本文档随项目发展持续更新，请定期查阅最新版本
