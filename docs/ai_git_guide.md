# AI助手Git操作指南

> 🤖 专为AI助手设计的Git操作规范和最佳实践

## 🎯 AI助手职责

### 主要任务
1. **代码审查**: 检查代码质量、格式、安全性
2. **自动化操作**: 执行标准化的Git工作流
3. **文档维护**: 更新文档、注释、README
4. **问题诊断**: 识别和解决Git相关问题
5. **最佳实践**: 确保遵循项目规范

### 操作权限
- ✅ 查看仓库状态和历史
- ✅ 创建和切换分支
- ✅ 提交代码更改
- ✅ 同步上游更新
- ❌ 直接推送到main分支
- ❌ 删除重要分支
- ❌ 强制推送覆盖历史

## 🔧 标准操作流程

### 1. 项目状态检查
```bash
# 使用工作流脚本检查状态
./scripts/git-workflow.sh status

# 或手动检查
git status
git log --oneline -5
git remote -v
```

**AI检查清单**:
- [ ] 工作区是否干净
- [ ] 是否有未提交的更改
- [ ] 当前分支是否正确
- [ ] 是否与上游同步

### 2. 代码更改流程
```bash
# 1. 创建功能分支
./scripts/git-workflow.sh feature <feature-name>

# 2. 进行代码更改
# (AI执行具体的代码修改)

# 3. 检查更改
git diff
git status

# 4. 提交更改
./scripts/git-workflow.sh commit

# 5. 推送分支
git push origin feature/<feature-name>
```

### 3. 同步上游更新
```bash
# 使用脚本同步
./scripts/git-workflow.sh sync

# 手动同步
git fetch upstream
git checkout main
git merge upstream/main
git push origin main
```

## 📝 AI提交规范

### 提交信息模板
```
<类型>(<范围>): <AI操作描述>

<详细说明>
- 具体更改内容
- 影响范围
- 相关文件

AI-Generated: <AI助手名称>
```

### AI专用类型
- `ai-feat`: AI添加的新功能
- `ai-fix`: AI修复的问题
- `ai-docs`: AI更新的文档
- `ai-refactor`: AI重构的代码
- `ai-optimize`: AI性能优化
- `ai-format`: AI代码格式化
- `ai-test`: AI添加的测试

### 提交示例
```
ai-docs(git): 添加AI助手Git操作指南

- 创建AI专用的Git操作规范
- 包含标准流程和最佳实践
- 添加安全检查清单

AI-Generated: Augment Agent
```

## 🛡️ 安全检查清单

### 提交前检查
- [ ] 代码中无硬编码密钥/密码
- [ ] 无个人敏感信息
- [ ] 文件大小合理 (<10MB)
- [ ] 代码格式符合规范
- [ ] 测试通过
- [ ] 文档已更新

### 分支操作检查
- [ ] 分支名称符合规范
- [ ] 基于最新的main分支
- [ ] 无冲突文件
- [ ] 提交历史清晰

### 推送前检查
- [ ] 目标分支正确
- [ ] 无强制推送
- [ ] 权限验证通过
- [ ] CI/CD检查通过

## 🚨 错误处理

### 常见错误及解决方案

#### 1. 合并冲突
```bash
# AI处理流程
git status  # 查看冲突文件
# 分析冲突内容
# 选择合适的解决方案
git add <resolved-files>
git commit -m "ai-fix: 解决合并冲突"
```

#### 2. 大文件问题
```bash
# 检查大文件
find . -type f -size +10M -not -path "./.git/*"

# 移除大文件
git rm --cached <large-file>
echo "<large-file>" >> .gitignore
git commit -m "ai-fix: 移除大文件并更新gitignore"
```

#### 3. 敏感信息泄露
```bash
# 立即停止操作
# 从历史中移除敏感文件
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch <sensitive-file>' \
--prune-empty --tag-name-filter cat -- --all

# 通知用户
echo "⚠️ 检测到敏感信息，已自动处理"
```

## 🔍 AI代码审查标准

### Python代码检查
```python
# 检查点
- 代码风格 (PEP 8)
- 导入排序
- 函数文档字符串
- 类型注解
- 异常处理
- 安全漏洞
```

### JavaScript/TypeScript检查
```javascript
// 检查点
- ESLint规则
- 类型定义
- 组件结构
- 性能优化
- 安全性
```

### 文档检查
```markdown
# 检查点
- 格式规范
- 链接有效性
- 代码示例正确性
- 多语言支持
- 更新日期
```

## 📊 AI操作报告

### 操作日志格式
```json
{
  "timestamp": "2025-06-16T10:30:00Z",
  "ai_agent": "Augment Agent",
  "operation": "code_review",
  "branch": "feature/user-auth",
  "files_modified": ["auth.py", "login.tsx"],
  "issues_found": 2,
  "issues_fixed": 2,
  "commit_hash": "abc123...",
  "status": "success"
}
```

### 性能指标
- 操作成功率
- 平均处理时间
- 发现问题数量
- 自动修复率
- 用户满意度

## 🤝 与人类协作

### 需要人类确认的操作
- 删除重要文件
- 大规模重构
- 修改核心配置
- 解决复杂冲突
- 发布版本

### 沟通模板
```
🤖 AI助手报告:

操作: <操作类型>
状态: <成功/失败/需要确认>
详情: <具体说明>

建议: <下一步操作建议>

需要人类干预: <是/否>
原因: <说明原因>
```

## 📚 学习和改进

### AI学习点
1. **用户反馈**: 收集用户对AI操作的反馈
2. **错误分析**: 分析失败操作的原因
3. **最佳实践**: 总结成功操作的模式
4. **规范更新**: 根据项目发展更新规范

### 持续优化
- 定期更新操作流程
- 改进错误检测算法
- 优化代码审查标准
- 增强安全检查能力

---

> 🔄 **版本**: v1.0
> 📅 **更新**: 2025-06-16
> 🤖 **适用**: 所有AI助手
> 📝 **维护**: 项目团队
