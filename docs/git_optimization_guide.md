# Git 优化指南

> 本文档记录了 magentic-ui 项目的 Git 配置优化方案，包含工作流程、配置说明和最佳实践。

## 📋 目录

- [项目概述](#项目概述)
- [Git 配置优化](#git-配置优化)
- [工作流脚本](#工作流脚本)
- [提交规范](#提交规范)
- [分支管理](#分支管理)
- [常用命令](#常用命令)
- [故障排除](#故障排除)

## 🎯 项目概述

### 仓库信息
- **项目名称**: magentic-ui
- **描述**: A research prototype of a human-centered web agent
- **主仓库**: https://github.com/microsoft/magentic-ui.git (upstream)
- **Fork仓库**: https://github.com/Poghappy/magentic-ui.git (origin)
- **主分支**: main

### 远程仓库配置
```bash
origin    https://github.com/Poghappy/magentic-ui.git (个人fork)
upstream  https://github.com/microsoft/magentic-ui.git (上游仓库)
```

## ⚙️ Git 配置优化

### 全局别名配置
```bash
git config --global alias.st status
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.lg "log --oneline --decorate --graph --all"
git config --global alias.unstage "reset HEAD --"
git config --global alias.last "log -1 HEAD"
```

### 优化设置
```bash
git config --global pull.rebase true          # pull时自动rebase
git config --global init.defaultBranch main   # 默认分支为main
git config --global core.autocrlf input       # 换行符处理
git config commit.template .gitmessage        # 提交信息模板
git config core.hooksPath .githooks           # 自定义hooks路径
```

### .gitignore 优化
项目的 `.gitignore` 文件已优化，包含：
- IDE和编辑器文件 (.vscode, .cursor, .idea)
- 操作系统生成文件 (.DS_Store, Thumbs.db)
- 项目特定文件 (.trae/, .venv-magentic/, config/local_*)
- 临时文件和缓存 (*.tmp, .cache/, logs/)
- 文档构建文件 (docs/_build/, docs/_archive/)

## 🚀 工作流脚本

### 脚本位置
`scripts/git-workflow.sh` - 自动化Git工作流脚本

### 可用命令
```bash
./scripts/git-workflow.sh status          # 检查Git状态
./scripts/git-workflow.sh sync            # 同步上游更新
./scripts/git-workflow.sh cleanup         # 清理工作区
./scripts/git-workflow.sh commit          # 提交当前更改
./scripts/git-workflow.sh push            # 推送到远程
./scripts/git-workflow.sh feature <name>  # 创建新功能分支
./scripts/git-workflow.sh workflow        # 执行完整工作流
./scripts/git-workflow.sh help            # 显示帮助信息
```

### 完整工作流程
```bash
# 一键执行完整工作流
./scripts/git-workflow.sh workflow
```
此命令会依次执行：
1. 检查当前状态
2. 提交未提交的更改
3. 同步上游更新
4. 推送到远程仓库
5. 清理工作区

## 📝 提交规范

### 提交信息格式
```
<类型>(<范围>): <主题>

<详细描述>

<相关Issue>
```

### 类型说明
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构代码
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动
- `perf`: 性能优化
- `ci`: CI/CD相关
- `build`: 构建系统或外部依赖的变动

### 范围说明
- `frontend`: 前端相关
- `backend`: 后端相关
- `api`: API相关
- `ui`: 用户界面
- `docs`: 文档
- `config`: 配置文件
- `deps`: 依赖管理

### 提交示例
```
feat(frontend): 添加用户登录功能

- 实现用户名密码登录
- 添加记住登录状态功能
- 集成OAuth第三方登录

Closes #123
```

## 🌿 分支管理

### 分支命名规范
- `main`: 主分支
- `feature/<功能名>`: 功能分支
- `fix/<问题描述>`: 修复分支
- `docs/<文档类型>`: 文档分支
- `refactor/<重构内容>`: 重构分支

### 创建功能分支
```bash
# 使用脚本创建
./scripts/git-workflow.sh feature user-authentication

# 手动创建
git checkout main
git pull upstream main
git checkout -b feature/user-authentication
```

### 分支合并流程
1. 在功能分支完成开发
2. 同步主分支最新更改
3. 解决冲突（如有）
4. 创建Pull Request
5. 代码审查
6. 合并到主分支

## 🔧 常用命令

### 日常操作
```bash
# 查看状态（使用别名）
git st

# 美化日志显示
git lg

# 同步上游更新
git fetch upstream
git merge upstream/main

# 取消暂存文件
git unstage <file>

# 查看最后一次提交
git last
```

### 高级操作
```bash
# 交互式rebase
git rebase -i HEAD~3

# 修改最后一次提交
git commit --amend

# 暂存当前更改
git stash
git stash pop

# 查看文件历史
git log --follow <file>
```

## 🔍 代码质量检查

### Pre-commit Hook
位置：`.githooks/pre-commit`

自动检查：
- Python代码格式 (black)
- Python导入排序 (isort)
- 前端代码规范 (eslint)
- 大文件检测 (>10MB)
- 敏感信息扫描

### 手动运行检查
```bash
# Python代码格式化
black .
isort .

# 前端代码检查
cd frontend && npm run lint
```

## 🚨 故障排除

### 常见问题

#### 1. 合并冲突
```bash
# 查看冲突文件
git status

# 手动解决冲突后
git add <resolved-files>
git commit
```

#### 2. 误提交敏感信息
```bash
# 从历史中移除文件
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch <sensitive-file>' \
--prune-empty --tag-name-filter cat -- --all
```

#### 3. 重置到上游状态
```bash
# 硬重置到上游main分支
git fetch upstream
git reset --hard upstream/main
git push origin main --force
```

#### 4. 清理本地分支
```bash
# 删除已合并的分支
git branch --merged | grep -v "\*\|main" | xargs -n 1 git branch -d

# 清理远程跟踪分支
git remote prune origin
```

## 📚 参考资源

- [Git官方文档](https://git-scm.com/doc)
- [GitHub Flow](https://guides.github.com/introduction/flow/)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [Git最佳实践](https://github.com/git-tips/tips)

## 🤝 贡献指南

1. Fork项目到个人账户
2. 创建功能分支
3. 遵循提交规范
4. 确保代码质量检查通过
5. 创建Pull Request
6. 等待代码审查

---

> 📝 **注意**: 本文档会随着项目发展持续更新，建议定期查阅最新版本。
> 
> 🔄 **最后更新**: 2025-06-15
> 
> 👤 **维护者**: Poghappy
