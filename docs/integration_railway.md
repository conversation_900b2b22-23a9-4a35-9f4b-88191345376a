# Railway 云平台集成与授权说明

## 1. 授权 API Key
- 当前已配置 3 天有效的 Railway API Key：`************************************`
- 已写入：
  - `config/railway.json`
  - `config/secrets.env`

## 2. 权限说明
- Cursor AI Agent 具备对 Railway 云平台的全部操作权限（API Token 级别）。
- 可自动化执行：
  - 项目/服务部署、环境变量管理、日志查询、扩缩容、服务重启等
  - 通过 Railway CLI/API 远程操作

## 3. 使用方式
- 代码/脚本/CI/CD 可自动读取 `RAILWAY_TOKEN` 环境变量或 `config/railway.json` 文件
- 推荐通过 secrets.env 统一管理敏感信息

## 4. 安全建议
- 该 API Key 有效期 3 天，过期后请及时更新
- 不要将 secrets.env/railway.json 提交到公开仓库
- 如需长期自动化，建议使用 Railway 长期有效 Token

## 5. 参考资料
- [Railway 官网](https://railway.com/)
- [Railway API 文档](https://docs.railway.app/develop/cli)

---

> 本文档由 Cursor AI Agent 自动生成，用于项目自动化与权限管理归档。

> 📝 **最后更新**: 2025-06-15
