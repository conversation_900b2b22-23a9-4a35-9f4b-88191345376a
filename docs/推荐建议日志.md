# 推荐集成工具与平台建议日志（持续补充）

## 4. 新集成/踩坑经验归档

- 2025-06-15：
  - FlowiseAI Docker 部署端口冲突，已自动切换 3010 端口，建议后续统一端口管理。
  - Hyperbrowser MCP 官方无公开 Docker 镜像，推荐本地 npx/uvx/pip 启动。
  - Apify API 测试需配置有效 Token，否则 401。
  - n8n API 测试需本地服务启动，端口 5678。
- 2025-06-14：
  - Railway 云端 Open Manus 部署体验良好，建议付费用户优先使用。
  - 日志目录建议统一 logs/，便于归档与分析。

## 5. 后续建议

- 每次新集成/踩坑经验及时归档，便于团队知识积累。
- 建议在 docs/ 目录下创建专门的集成文档，详细记录每个工具的配置、API 调用、常见问题等。
- 对于新集成的工具，建议先在测试环境验证，确认无误后再部署到生产环境。
- 定期更新集成文档，确保信息的准确性和时效性。

> 📝 **最后更新**: 2025-06-15
