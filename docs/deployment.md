# 部署说明文档

## 部署指南

## 1. 部署环境要求

- 支持平台：macOS/Linux/（可选）云服务器（如 AWS EC2、DigitalOcean）
- 依赖：Python 3.10+/Node.js 18+/Docker（推荐）、Git
- API密钥管理：所有第三方服务（Apify、Notion、n8n等）API Key/OAuth2 Token 需集中存放于 config/secrets.env，严禁入库
- 网络要求：可访问外部API与云服务

## 2. 部署步骤

### 2.1 OpenManus Agent 本地部署（推荐）

基于实际部署经验，OpenManus Agent 在本地部署最为稳定可靠：

1. **环境准备**
   ```bash
   cd agents/openmanus
   python -m venv venv-311
   source venv-311/bin/activate  # macOS/Linux
   # 或 venv-311\Scripts\activate  # Windows
   ```

2. **安装依赖**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **配置环境**
   - 复制 `config/config.example.toml` 为 `config/config.toml`
   - 配置必要的 API 密钥和模型设置

4. **启动服务**
   ```bash
   # 方式1：直接启动 FastAPI 服务
   uvicorn main:app --host 0.0.0.0 --port 8000
   
   # 方式2：使用脚本启动
   bash ../../scripts/start_openmanus.sh
   ```

5. **验证部署**
   - 访问 `http://localhost:8000` 查看服务状态
   - 访问 `http://localhost:8000/docs` 查看 API 文档
   - 访问 `http://localhost:8000/health` 进行健康检查

### 2.2 传统本地开发环境

1. 克隆代码仓库：`git clone ...`
2. 安装依赖：`pip install -r requirements.txt`、`npm install`（如有）
3. 配置环境变量：复制 `config/secrets.env.example` 为 `config/secrets.env`，填写API密钥
4. 启动服务：`docker-compose up` 或 `python main.py`/`npm start`

### 2.3 云端部署（Railway 等平台）

**注意：基于实际部署经验，云平台部署可能遇到以下问题：**
- ModuleNotFoundError 和依赖冲突
- Git 仓库归档导致的访问问题
- 502 错误和部署超时
- 环境变量配置复杂

**如需云端部署，建议：**
1. 选择云服务器，配置安全组与端口
2. 安装 Docker & Docker Compose
3. 上传代码与配置文件
4. 配置 secrets.env，确保密钥安全
5. 启动服务：`docker-compose up -d`
6. 配置定时任务（如需）

## 3. 运行与监控

### 3.1 OpenManus Agent 监控

- **启动命令**：`uvicorn main:app --host 0.0.0.0 --port 8000`
- **健康检查**：
  - 根路径：`GET http://localhost:8000/` - 返回服务基本信息
  - 健康检查：`GET http://localhost:8000/health` - 返回服务健康状态
  - API 文档：`GET http://localhost:8000/docs` - Swagger UI 接口文档
- **日志查看**：
  - 控制台输出：实时查看服务日志
  - 应用日志：检查 `app/logger.py` 配置的日志输出
- **性能监控**：
  - 内存使用：监控 Python 进程内存占用
  - CPU 使用率：关注高负载时的响应时间
  - API 响应时间：通过 `/health` 端点监控服务响应

### 3.2 传统服务监控

- 启动命令：`docker-compose up` 或 `python main.py`
- 日志查看：`docker logs <container>` 或 `logs/` 目录
- 健康检查：定期访问 `/health` 接口或查看监控面板
- 异常告警：建议集成 n8n/Make.com/Zapier 通知

## 4. 常见问题与排查建议

### 4.1 OpenManus Agent 特定问题

- **ModuleNotFoundError**：
  - 确保在正确的虚拟环境中运行：`source venv-311/bin/activate`
  - 重新安装依赖：`pip install -r requirements.txt`
  - 检查 Python 路径：`which python` 和 `python --version`

- **FastAPI 启动失败**：
  - 检查端口占用：`lsof -i :8000`
  - 验证 main.py 语法：`python -m py_compile main.py`
  - 查看详细错误：`uvicorn main:app --host 0.0.0.0 --port 8000 --log-level debug`

- **API 调用异常**：
  - 检查配置文件：确认 `config/config.toml` 中的 API 密钥正确
  - 网络连接：测试外部 API 的可达性
  - 权限问题：确认 API 密钥有足够的权限

- **云平台部署问题**：
  - Railway 部署失败：建议使用本地部署，避免云平台的复杂性
  - Docker 构建失败：检查 Dockerfile 中的依赖安装
  - 环境变量配置：确保所有必需的环境变量都已设置

### 4.2 通用问题

- 依赖安装失败：检查 Python/Node.js 版本、网络代理
- API调用异常：确认 API Key/Token 正确、网络可达
- 日志无输出：检查日志配置与权限
- 服务无法启动：查看端口占用、依赖服务状态
- 第三方服务变更：关注官方文档，及时更新集成

---
> 建议定期备份 config/secrets.env 与 logs/，并关注第三方服务 API 变更公告。

> 📝 **最后更新**: 2025-06-15
