# 核心接口文档（细化版）

## 1. Agent 调用接口
- **路径**：/api/agent/execute
- **方法**：POST
- **输入参数**：
  - `instruction` (string)：自然语言指令
  - `context` (object, 可选)：上下文信息
- **返回格式**：
  - `status` (string)：success | error
  - `result` (object)：任务执行结果
  - `logs` (array)：执行日志
  - `error` (object, 可选)：错误信息
- **错误码示例**：
  - 400 参数错误
  - 500 内部错误

## 2. 插件/第三方API集成接口
- **路径**：/api/plugin/call
- **方法**：POST
- **输入参数**：
  - `plugin` (string)：插件名
  - `params` (object)：API请求参数
  - `auth` (object, 可选)：认证信息
- **返回格式**：
  - `status` (string)
  - `data` (object)
  - `error` (object, 可选)
- **错误码示例**：
  - 401 未授权
  - 404 插件不存在
  - 500 插件内部错误

## 3. 任务链/批量任务接口
- **路径**：/api/taskchain/execute
- **方法**：POST
- **输入参数**：
  - `chain` (array)：任务链描述（JSON/YAML）
- **返回格式**：
  - `status` (string)
  - `results` (array)：各子任务执行状态与结果
  - `error` (object, 可选)
- **错误码示例**：
  - 400 任务链格式错误
  - 500 执行异常

## 4. 日志与监控接口
- **路径**：/api/logs/query
- **方法**：GET/POST
- **输入参数**：
  - `query` (object)：日志查询参数
- **返回格式**：
  - `status` (string)
  - `logs` (array)
  - `error` (object, 可选)
- **错误码示例**：
  - 403 权限不足
  - 500 查询异常

## 5. 部署与配置接口
- **路径**：/api/deploy/status
- **方法**：GET
- **输入参数**：无
- **返回格式**：
  - `status` (string)
  - `env` (object)：环境变量
  - `health` (object)：服务健康检查
  - `error` (object, 可选)
- **错误码示例**：
  - 500 服务异常

> 📝 **最后更新**: 2025-06-15
