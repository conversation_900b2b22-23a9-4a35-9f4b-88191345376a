# Git 快速参考

> 🚀 magentic-ui 项目 Git 操作速查表

## 🔧 快速设置

```bash
# 克隆项目
git clone https://github.com/Poghappy/magentic-ui.git
cd magentic-ui

# 添加上游仓库
git remote add upstream https://github.com/microsoft/magentic-ui.git

# 设置Git hooks
git config core.hooksPath .githooks
```

## ⚡ 常用别名

| 别名 | 完整命令 | 说明 |
|------|----------|------|
| `git st` | `git status` | 查看状态 |
| `git co` | `git checkout` | 切换分支 |
| `git br` | `git branch` | 分支操作 |
| `git ci` | `git commit` | 提交更改 |
| `git lg` | `git log --oneline --decorate --graph --all` | 美化日志 |
| `git unstage` | `git reset HEAD --` | 取消暂存 |
| `git last` | `git log -1 HEAD` | 最后提交 |

## 🚀 工作流脚本

```bash
# 检查状态
./scripts/git-workflow.sh status

# 提交更改
./scripts/git-workflow.sh commit

# 同步上游
./scripts/git-workflow.sh sync

# 完整工作流
./scripts/git-workflow.sh workflow

# 创建功能分支
./scripts/git-workflow.sh feature <branch-name>
```

## 📝 提交格式

```
<类型>(<范围>): <主题>

<详细描述>

<相关Issue>
```

### 常用类型
- `feat`: 新功能
- `fix`: 修复
- `docs`: 文档
- `style`: 格式
- `refactor`: 重构
- `test`: 测试
- `chore`: 杂项

### 示例
```
feat(frontend): 添加用户登录功能

- 实现用户名密码登录
- 添加OAuth集成

Closes #123
```

## 🌿 分支操作

```bash
# 创建并切换到新分支
git co -b feature/new-feature

# 同步主分支
git co main
git fetch upstream
git merge upstream/main

# 推送分支
git push origin feature/new-feature

# 删除本地分支
git br -d feature/old-feature

# 删除远程分支
git push origin --delete feature/old-feature
```

## 🔄 同步流程

```bash
# 1. 获取上游更新
git fetch upstream

# 2. 切换到主分支
git co main

# 3. 合并上游更改
git merge upstream/main

# 4. 推送到个人仓库
git push origin main

# 5. 更新功能分支（可选）
git co feature/my-feature
git rebase main
```

## 🚨 紧急操作

### 撤销操作
```bash
# 撤销最后一次提交（保留更改）
git reset --soft HEAD~1

# 撤销最后一次提交（丢弃更改）
git reset --hard HEAD~1

# 撤销文件更改
git co -- <file>

# 撤销所有未暂存更改
git co .
```

### 冲突解决
```bash
# 查看冲突文件
git st

# 编辑冲突文件后
git add <resolved-file>
git commit

# 中止合并
git merge --abort
```

### 暂存操作
```bash
# 暂存当前更改
git stash

# 查看暂存列表
git stash list

# 恢复暂存
git stash pop

# 删除暂存
git stash drop
```

## 🔍 查看信息

```bash
# 查看提交历史
git lg

# 查看文件更改
git diff
git diff --staged

# 查看分支
git br -a

# 查看远程仓库
git remote -v

# 查看文件历史
git log --follow <file>

# 查看某次提交
git show <commit-hash>
```

## 🛠️ 代码质量

### 格式化代码
```bash
# Python代码格式化
black .
isort .

# 前端代码检查
cd frontend && npm run lint
```

### 检查大文件
```bash
# 查找大文件 (>10MB)
find . -type f -size +10M -not -path "./.git/*"
```

## 📊 统计信息

```bash
# 提交统计
git shortlog -sn

# 代码行数统计
git ls-files | xargs wc -l

# 贡献者统计
git log --format='%aN' | sort -u | wc -l
```

## 🔗 有用链接

- [完整Git优化指南](./git_optimization_guide.md)
- [项目README](../README.md)
- [贡献指南](../CONTRIBUTING.md)

---

> 💡 **提示**: 使用 `./scripts/git-workflow.sh help` 查看工作流脚本的完整帮助信息

> 📝 **最后更新**: 2025-06-15
