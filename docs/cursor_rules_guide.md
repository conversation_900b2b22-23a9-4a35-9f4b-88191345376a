# Cursor 规则管理指南

> 🎯 Cursor AI 提示词管理最佳实践和使用指南

## 📋 目录

- [规则结构概述](#规则结构概述)
- [规则类型说明](#规则类型说明)
- [使用方法](#使用方法)
- [最佳实践](#最佳实践)
- [维护指南](#维护指南)

## 🏗️ 规则结构概述

### 当前规则组织

```
项目根目录/
├── .cursor/
│   └── rules/                    # 新版本 MDC 格式规则
│       ├── project-core.mdc      # 项目核心规则 (Always)
│       ├── documentation.mdc     # 文档维护规则 (Auto Attached)
│       └── development.mdc       # 开发规范规则 (Auto Attached)
├── .cursorrules                  # 兼容性规则文件 (Legacy)
└── .trae/                        # 原有规则目录 (保留)
    └── rules/
        ├── project_rules.md      # 项目规则
        ├── global_rules.md       # 全局规则
        └── user_rules.md         # 用户规则
```

### 规则迁移状态

| 原文件 | 新文件 | 状态 | 说明 |
|--------|--------|------|------|
| `.trae/rules/project_rules.md` | `.cursor/rules/project-core.mdc` | ✅ 已迁移 | 项目核心规则 |
| `.trae/rules/global_rules.md` | `.cursor/rules/documentation.mdc` | ✅ 已迁移 | 文档维护规则 |
| `.trae/rules/user_rules.md` | `.cursorrules` | ✅ 已迁移 | 用户偏好设置 |

## 📝 规则类型说明

### 1. Always Rules (始终应用)
- **文件**: `.cursor/rules/project-core.mdc`
- **作用**: 项目核心规则，始终包含在 AI 上下文中
- **内容**: 项目概述、架构规范、技术选型原则

### 2. Auto Attached Rules (自动附加)
- **文件**: `.cursor/rules/documentation.mdc`, `.cursor/rules/development.mdc`
- **作用**: 当匹配特定文件模式时自动应用
- **触发条件**: 
  - `documentation.mdc`: 编辑 `docs/**/*` 或 `*.md` 文件时
  - `development.mdc`: 编辑代码文件时

### 3. Legacy Rules (兼容性规则)
- **文件**: `.cursorrules`
- **作用**: 向后兼容，包含用户偏好和通用规范
- **状态**: 仍然支持，但推荐使用新格式

## 🚀 使用方法

### 在 Cursor 中启用规则

1. **自动应用**: 项目核心规则会自动应用到所有对话
2. **文件触发**: 编辑特定类型文件时自动加载相关规则
3. **手动引用**: 使用 `@project-core` 等方式手动引用规则

### 查看当前规则状态

```bash
# 在 Cursor 中使用命令面板
Cmd/Ctrl + Shift + P > "Cursor Rules: View Rules"

# 或在设置中查看
Cursor Settings > Rules
```

### 创建新规则

```bash
# 使用命令面板创建
Cmd/Ctrl + Shift + P > "New Cursor Rule"

# 或手动创建 MDC 文件
touch .cursor/rules/new-rule.mdc
```

## 💡 最佳实践

### 1. 规则组织原则

#### 按功能分类
- **核心规则**: 项目基础信息和架构规范
- **领域规则**: 特定技术栈或功能模块的规则
- **工具规则**: 开发工具和流程规范

#### 按应用范围分类
- **全局规则**: 适用于整个项目
- **目录规则**: 适用于特定目录
- **文件规则**: 适用于特定文件类型

### 2. 规则编写规范

#### MDC 文件格式
```yaml
---
description: 规则描述
globs: ["**/*.py", "**/*.js"]  # 文件匹配模式
alwaysApply: false             # 是否始终应用
---

# 规则内容 (Markdown 格式)

## 规则说明

具体的规则内容...

@referenced-file.md            # 引用其他文件
```

#### 内容组织
- **简洁明确**: 避免冗长的描述
- **具体可操作**: 提供具体的指导和示例
- **结构化**: 使用标题和列表组织内容
- **引用文件**: 使用 `@filename` 引用相关文件

### 3. 规则维护策略

#### 定期更新
- **项目变更**: 架构或技术栈变更时更新规则
- **最佳实践**: 发现新的最佳实践时补充规则
- **问题修复**: 发现规则问题时及时修正

#### 版本控制
- **Git 管理**: 将规则文件纳入版本控制
- **变更记录**: 重要变更在 commit 中说明
- **团队同步**: 确保团队成员使用最新规则

## 🔧 维护指南

### 规则文件检查

```bash
# 检查规则文件语法
# (Cursor 会自动验证 MDC 格式)

# 查看规则应用状态
# 在 Cursor 设置中查看 Rules 页面
```

### 规则效果验证

1. **创建测试对话**: 验证规则是否正确应用
2. **检查上下文**: 确认相关文件被正确引用
3. **测试触发条件**: 验证 Auto Attached 规则的触发条件

### 规则优化建议

#### 性能优化
- **控制规则大小**: 单个规则文件不超过 500 行
- **合理使用引用**: 避免引用过多文件
- **精确匹配模式**: 使用精确的 glob 模式

#### 可维护性
- **模块化设计**: 将大规则拆分为多个小规则
- **清晰命名**: 使用描述性的文件名
- **文档说明**: 为复杂规则添加说明文档

## 📊 规则使用统计

### 当前规则概况
- **规则文件数**: 4个 (3个新格式 + 1个兼容格式)
- **覆盖范围**: 项目核心、文档维护、开发规范、用户偏好
- **自动化程度**: 80% (大部分规则自动应用)

### 规则应用场景
- **代码开发**: 自动应用开发规范和项目核心规则
- **文档编辑**: 自动应用文档维护规则
- **项目管理**: 始终应用项目核心规则
- **问题排查**: 手动引用相关规则

## 🔗 相关资源

### 官方文档
- [Cursor Rules 官方文档](https://docs.cursor.com/context/rules)
- [MDC 格式说明](https://docs.cursor.com/context/rules#example-mdc-rule)

### 社区资源
- [Awesome Cursor Rules](https://github.com/PatrickJS/awesome-cursorrules)
- [Cursor Rules 示例集合](https://cursor.directory/)

### 项目文档
- [AI 文档维护指南](ai_documentation_guide.md)
- [项目概览](../PROJECT_OVERVIEW.md)
- [文档维护日志](MAINTENANCE_LOG.md)

---

> 📝 **文档信息**
> 
> - **创建日期**: 2025-06-16
> - **最后更新**: 2025-06-16
> - **维护者**: AI助手团队
> - **文档版本**: v1.0
> 
> 🔄 **更新说明**: 基于 Cursor 官方最佳实践编写，提供完整的规则管理指南
