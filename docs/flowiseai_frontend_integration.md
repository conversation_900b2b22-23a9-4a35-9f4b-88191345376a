# FlowiseAI 前端集成与启动说明（自动化部署更新）

## 1. 目录结构 Structure

- 前端可视化位于 Located at: `ui/flowise/`

## 2. Docker 部署与启动 Deployment & Startup

1. 已自动生成 `docker-compose.yml`，端口映射为 3010:3000。
2. 启动服务：

   ```bash
   cd ui/flowise
   docker compose up -d
   ```

3. 访问本地前端 Access frontend: <http://localhost:3010>
   - 默认管理员账号 Default admin: <<EMAIL>>
   - 默认密码 Password: admin123
   - 首次登录后请立即修改密码 Please change password after first login

## 3. 与 Open Manus 集成 Integration

- 通过自定义节点/API/Webhook，将 Open Manus 的任务流、日志、浏览器操作画面推送到 FlowiseAI。
- 配置 WebSocket/API 通信，实现自然语言驱动任务流。

## 4. 常见问题 FAQ

- 端口冲突 Port conflict: 已自动切换为 3010。
- 依赖问题 Dependency: Docker 部署无需本地 Node 环境。

## 5. 参考文档 Reference

- [FlowiseAI 官方文档 Official Docs](https://docs.flowiseai.com/)
- [Open Manus API 文档](./接口文档.md)

---
如需自动化部署脚本或定制化集成方案，请联系平台运维。
For automated deployment scripts or custom integration, contact platform ops.

> 📝 **最后更新**: 2025-06-15
