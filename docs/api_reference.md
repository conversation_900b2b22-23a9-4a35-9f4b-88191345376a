# API 参考文档

> 🔌 OpenManus Agent API 接口完整参考

## 📋 目录

- [基础信息](#基础信息)
- [认证方式](#认证方式)
- [核心接口](#核心接口)
- [错误处理](#错误处理)
- [示例代码](#示例代码)

## 🎯 基础信息

### 服务地址
- **本地开发**: `http://localhost:8000`
- **生产环境**: 根据部署配置

### API版本
- **当前版本**: v1.0
- **兼容性**: 向后兼容

### 数据格式
- **请求格式**: JSON
- **响应格式**: JSON
- **字符编码**: UTF-8

## 🔐 认证方式

### API Key认证
```http
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

### 获取API Key
```bash
# 通过配置文件获取
cat config/secrets.env | grep API_KEY
```

## 🚀 核心接口

### 1. 服务状态

#### GET /
获取服务基本信息和状态

**请求示例:**
```bash
curl -X GET http://localhost:8000/
```

**响应示例:**
```json
{
  "message": "OpenManus Agent API is running",
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2025-06-16T10:30:00Z"
}
```

### 2. 健康检查

#### GET /health
检查服务健康状态

**请求示例:**
```bash
curl -X GET http://localhost:8000/health
```

**响应示例:**
```json
{
  "status": "healthy",
  "service": "openmanus-agent",
  "timestamp": **********.123,
  "checks": {
    "database": "ok",
    "external_apis": "ok",
    "memory_usage": "normal"
  }
}
```

### 3. 任务处理

#### POST /process
处理用户任务请求

**请求参数:**
```json
{
  "instruction": "string",     // 必需：自然语言指令
  "context": {                 // 可选：上下文信息
    "user_id": "string",
    "session_id": "string",
    "preferences": {}
  },
  "options": {                 // 可选：执行选项
    "timeout": 300,
    "priority": "normal",
    "async": false
  }
}
```

**请求示例:**
```bash
curl -X POST http://localhost:8000/process \
  -H "Content-Type: application/json" \
  -d '{
    "instruction": "帮我搜索最新的AI新闻",
    "context": {
      "user_id": "user123",
      "session_id": "session456"
    }
  }'
```

**响应示例:**
```json
{
  "status": "success",
  "task_id": "task_789",
  "result": {
    "type": "search_results",
    "data": [
      {
        "title": "AI新闻标题",
        "url": "https://example.com/news1",
        "summary": "新闻摘要"
      }
    ]
  },
  "execution_time": 2.5,
  "logs": [
    {
      "timestamp": "2025-06-16T10:30:01Z",
      "level": "info",
      "message": "开始执行搜索任务"
    }
  ]
}
```

### 4. 任务状态查询

#### GET /tasks/{task_id}
查询特定任务的执行状态

**请求示例:**
```bash
curl -X GET http://localhost:8000/tasks/task_789
```

**响应示例:**
```json
{
  "task_id": "task_789",
  "status": "completed",
  "progress": 100,
  "created_at": "2025-06-16T10:30:00Z",
  "completed_at": "2025-06-16T10:30:05Z",
  "result": {
    "type": "search_results",
    "data": []
  }
}
```

### 5. 插件管理

#### GET /plugins
获取可用插件列表

**响应示例:**
```json
{
  "plugins": [
    {
      "name": "browser_automation",
      "version": "1.0.0",
      "status": "active",
      "description": "浏览器自动化插件"
    },
    {
      "name": "content_crawler",
      "version": "1.2.0",
      "status": "active",
      "description": "内容采集插件"
    }
  ]
}
```

#### POST /plugins/{plugin_name}/execute
执行特定插件功能

**请求参数:**
```json
{
  "action": "string",          // 必需：插件动作
  "parameters": {},            // 必需：动作参数
  "options": {}                // 可选：执行选项
}
```

## ❌ 错误处理

### 错误响应格式
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息",
    "timestamp": "2025-06-16T10:30:00Z"
  }
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `INVALID_REQUEST` | 400 | 请求参数无效 |
| `UNAUTHORIZED` | 401 | 认证失败 |
| `FORBIDDEN` | 403 | 权限不足 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `RATE_LIMITED` | 429 | 请求频率超限 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务不可用 |

## 💡 示例代码

### Python示例
```python
import requests
import json

# 基础配置
BASE_URL = "http://localhost:8000"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer YOUR_API_KEY"
}

# 处理任务
def process_task(instruction):
    url = f"{BASE_URL}/process"
    data = {
        "instruction": instruction,
        "context": {
            "user_id": "python_client"
        }
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()

# 使用示例
result = process_task("帮我搜索Python教程")
print(json.dumps(result, indent=2, ensure_ascii=False))
```

### JavaScript示例
```javascript
// 基础配置
const BASE_URL = "http://localhost:8000";
const headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer YOUR_API_KEY"
};

// 处理任务
async function processTask(instruction) {
    const response = await fetch(`${BASE_URL}/process`, {
        method: "POST",
        headers: headers,
        body: JSON.stringify({
            instruction: instruction,
            context: {
                user_id: "js_client"
            }
        })
    });
    
    return await response.json();
}

// 使用示例
processTask("帮我搜索JavaScript教程")
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

### cURL示例
```bash
# 健康检查
curl -X GET http://localhost:8000/health

# 处理任务
curl -X POST http://localhost:8000/process \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "instruction": "帮我查看今天的天气",
    "context": {
      "user_id": "curl_client"
    }
  }'

# 查询任务状态
curl -X GET http://localhost:8000/tasks/task_123 \
  -H "Authorization: Bearer YOUR_API_KEY"
```

## 📚 相关文档

- [部署指南](deployment.md) - 服务部署说明
- [配置指南](configuration_guide.md) - 配置管理
- [故障排除](troubleshooting.md) - 问题解决

---

> 📝 **文档信息**
> 
> - **创建日期**: 2025-06-16
> - **最后更新**: 2025-06-16
> - **维护者**: 开发团队
> - **文档版本**: v1.0
> 
> 🔄 **更新说明**: 基于实际API实现编写，持续更新中
