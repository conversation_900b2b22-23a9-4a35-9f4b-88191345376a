# 故障排除指南

> 🔧 OpenManus Agent 常见问题诊断和解决方案

## 📋 目录

- [快速诊断](#快速诊断)
- [服务启动问题](#服务启动问题)
- [API调用问题](#api调用问题)
- [依赖和环境问题](#依赖和环境问题)
- [性能问题](#性能问题)
- [配置问题](#配置问题)
- [日志分析](#日志分析)

## 🚀 快速诊断

### 一键健康检查
```bash
# 检查服务状态
curl -f http://localhost:8000/health || echo "服务不可用"

# 检查端口占用
lsof -i :8000

# 检查进程状态
ps aux | grep uvicorn
```

### 系统要求检查
```bash
# Python版本检查
python --version  # 需要 3.10+

# 虚拟环境检查
which python
echo $VIRTUAL_ENV

# 依赖检查
pip list | grep -E "(fastapi|uvicorn|pydantic)"
```

## 🚨 服务启动问题

### 问题1: ModuleNotFoundError

**症状:**
```
ModuleNotFoundError: No module named 'fastapi'
```

**解决方案:**
```bash
# 1. 确认虚拟环境激活
source venv-311/bin/activate

# 2. 重新安装依赖
pip install --upgrade pip
pip install -r requirements.txt

# 3. 验证安装
python -c "import fastapi; print('FastAPI安装成功')"
```

### 问题2: 端口被占用

**症状:**
```
OSError: [Errno 48] Address already in use
```

**解决方案:**
```bash
# 1. 查找占用进程
lsof -i :8000

# 2. 终止占用进程
kill -9 <PID>

# 3. 或使用其他端口
uvicorn main:app --host 0.0.0.0 --port 8001
```

### 问题3: 配置文件缺失

**症状:**
```
FileNotFoundError: config/config.toml not found
```

**解决方案:**
```bash
# 1. 复制配置模板
cp config/config.example.toml config/config.toml

# 2. 编辑配置文件
nano config/config.toml

# 3. 验证配置
python -c "import toml; print(toml.load('config/config.toml'))"
```

### 问题4: 权限问题

**症状:**
```
PermissionError: [Errno 13] Permission denied
```

**解决方案:**
```bash
# 1. 检查文件权限
ls -la config/
ls -la logs/

# 2. 修复权限
chmod 755 config/
chmod 644 config/*.toml
chmod 755 logs/

# 3. 检查目录所有者
chown -R $USER:$USER .
```

## 🔌 API调用问题

### 问题1: 401 Unauthorized

**症状:**
```json
{
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Invalid API key"
  }
}
```

**解决方案:**
```bash
# 1. 检查API密钥配置
cat config/secrets.env | grep API_KEY

# 2. 验证密钥格式
echo "Bearer YOUR_API_KEY" | wc -c  # 检查长度

# 3. 重新生成密钥
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

### 问题2: 500 Internal Server Error

**症状:**
```json
{
  "error": {
    "code": "INTERNAL_ERROR",
    "message": "Internal server error"
  }
}
```

**解决方案:**
```bash
# 1. 查看详细日志
tail -f logs/app.log

# 2. 启用调试模式
uvicorn main:app --host 0.0.0.0 --port 8000 --log-level debug

# 3. 检查依赖服务
curl -f http://localhost:5678/health  # n8n
curl -f https://api.apify.com/v2/acts  # Apify
```

### 问题3: 超时问题

**症状:**
```
TimeoutError: Request timeout after 30 seconds
```

**解决方案:**
```bash
# 1. 增加超时时间
curl -X POST http://localhost:8000/process \
  -H "Content-Type: application/json" \
  -d '{"instruction": "test", "options": {"timeout": 300}}'

# 2. 检查网络连接
ping google.com
curl -I https://api.openai.com

# 3. 优化任务复杂度
# 将复杂任务拆分为多个简单任务
```

## 📦 依赖和环境问题

### 问题1: Python版本不兼容

**症状:**
```
SyntaxError: invalid syntax (Python 3.8)
```

**解决方案:**
```bash
# 1. 检查Python版本
python --version

# 2. 安装Python 3.10+
# macOS
brew install python@3.10

# Ubuntu
sudo apt update
sudo apt install python3.10

# 3. 重新创建虚拟环境
python3.10 -m venv venv-311
source venv-311/bin/activate
```

### 问题2: 依赖冲突

**症状:**
```
ERROR: pip's dependency resolver does not currently consider all the ways that dependencies can conflict
```

**解决方案:**
```bash
# 1. 清理环境
pip freeze > requirements_backup.txt
pip uninstall -r requirements_backup.txt -y

# 2. 重新安装
pip install --upgrade pip setuptools wheel
pip install -r requirements.txt

# 3. 使用依赖管理工具
pip install pip-tools
pip-compile requirements.in
pip-sync requirements.txt
```

### 问题3: 系统依赖缺失

**症状:**
```
ImportError: libffi.so.6: cannot open shared object file
```

**解决方案:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install libffi-dev python3-dev build-essential

# macOS
brew install libffi
export LDFLAGS="-L$(brew --prefix libffi)/lib"
export CPPFLAGS="-I$(brew --prefix libffi)/include"

# CentOS/RHEL
sudo yum install libffi-devel python3-devel gcc
```

## ⚡ 性能问题

### 问题1: 内存使用过高

**症状:**
```
MemoryError: Unable to allocate memory
```

**解决方案:**
```bash
# 1. 监控内存使用
top -p $(pgrep -f uvicorn)
htop

# 2. 优化内存配置
export PYTHONMALLOC=malloc
ulimit -v 2097152  # 限制虚拟内存为2GB

# 3. 重启服务
pkill -f uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000
```

### 问题2: CPU使用率过高

**症状:**
```
CPU使用率持续100%
```

**解决方案:**
```bash
# 1. 分析CPU使用
top -p $(pgrep -f uvicorn)
perf top -p $(pgrep -f uvicorn)

# 2. 优化并发配置
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 2

# 3. 启用异步处理
# 在代码中使用 async/await
```

### 问题3: 响应时间过长

**症状:**
```
API响应时间超过10秒
```

**解决方案:**
```bash
# 1. 启用性能分析
pip install py-spy
py-spy top --pid $(pgrep -f uvicorn)

# 2. 优化数据库查询
# 添加索引，优化查询语句

# 3. 启用缓存
# 使用Redis或内存缓存
```

## ⚙️ 配置问题

### 问题1: 环境变量未生效

**症状:**
```
配置值为默认值，环境变量未被读取
```

**解决方案:**
```bash
# 1. 检查环境变量
env | grep -E "(OPENAI|APIFY|N8N)"

# 2. 加载环境变量
source config/secrets.env
export $(cat config/secrets.env | xargs)

# 3. 验证加载
python -c "import os; print(os.getenv('OPENAI_API_KEY', 'Not found'))"
```

### 问题2: 配置文件格式错误

**症状:**
```
toml.decoder.TomlDecodeError: Invalid TOML
```

**解决方案:**
```bash
# 1. 验证TOML格式
python -c "import toml; toml.load('config/config.toml')"

# 2. 使用在线验证器
# https://www.toml-lint.com/

# 3. 重新生成配置
cp config/config.example.toml config/config.toml
```

## 📊 日志分析

### 启用详细日志
```bash
# 1. 修改日志级别
export LOG_LEVEL=DEBUG

# 2. 启用文件日志
mkdir -p logs
touch logs/app.log

# 3. 实时查看日志
tail -f logs/app.log | grep ERROR
```

### 常见日志模式
```bash
# 查找错误
grep -E "(ERROR|CRITICAL)" logs/app.log

# 查找性能问题
grep -E "(slow|timeout|memory)" logs/app.log

# 查找API调用
grep -E "(POST|GET|PUT|DELETE)" logs/app.log
```

### 日志轮转配置
```bash
# 安装logrotate
sudo apt install logrotate

# 配置文件 /etc/logrotate.d/openmanus
/path/to/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 user group
}
```

## 🆘 紧急恢复

### 服务完全无法启动
```bash
# 1. 备份当前状态
cp -r . ../openmanus_backup_$(date +%Y%m%d_%H%M%S)

# 2. 重置到已知工作状态
git stash
git checkout main
git pull origin main

# 3. 重新部署
rm -rf venv-311
python -m venv venv-311
source venv-311/bin/activate
pip install -r requirements.txt

# 4. 验证恢复
uvicorn main:app --host 0.0.0.0 --port 8000
```

### 数据损坏恢复
```bash
# 1. 停止服务
pkill -f uvicorn

# 2. 恢复备份数据
cp -r backup/data/* data/

# 3. 验证数据完整性
python scripts/verify_data.py

# 4. 重启服务
uvicorn main:app --host 0.0.0.0 --port 8000
```

## 📞 获取帮助

### 社区支持
- GitHub Issues: [项目Issues页面]
- 文档: [docs/README.md](README.md)
- API文档: [docs/api_reference.md](api_reference.md)

### 专业支持
- 技术支持邮箱: <EMAIL>
- 紧急联系: +86-xxx-xxxx-xxxx

---

> 📝 **文档信息**
> 
> - **创建日期**: 2025-06-16
> - **最后更新**: 2025-06-16
> - **维护者**: 运维团队
> - **文档版本**: v1.0
> 
> 🔄 **更新说明**: 基于实际运维经验编写，持续更新中
