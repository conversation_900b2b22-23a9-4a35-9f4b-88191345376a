# 文档标题

> 📝 简短的文档描述，说明本文档的目的和适用范围

## 📋 目录

- [概述](#概述)
- [主要内容](#主要内容)
- [使用方法](#使用方法)
- [示例](#示例)
- [注意事项](#注意事项)
- [相关链接](#相关链接)

## 🎯 概述

### 背景
简要说明为什么需要这个文档，解决什么问题。

### 目标
明确说明文档的目标和预期效果。

### 适用范围
说明文档适用的场景、用户群体或系统版本。

## 📚 主要内容

### 核心概念
解释文档中涉及的重要概念和术语。

### 详细说明
提供详细的说明、配置或操作步骤。

## 🚀 使用方法

### 前置条件
列出使用前需要满足的条件。

### 操作步骤
1. 第一步操作
2. 第二步操作
3. 第三步操作

### 配置说明
如果涉及配置，提供详细的配置说明。

## 💡 示例

### 基础示例
```bash
# 提供具体的代码示例
echo "Hello World"
```

### 高级示例
```python
# 更复杂的示例
def example_function():
    return "示例代码"
```

## ⚠️ 注意事项

### 常见问题
- 问题1：描述和解决方案
- 问题2：描述和解决方案

### 最佳实践
- 建议1：具体建议
- 建议2：具体建议

### 限制和约束
说明使用过程中的限制条件。

## 🔗 相关链接

- [相关文档1](./related_doc1.md) - 简短描述
- [相关文档2](./related_doc2.md) - 简短描述
- [外部链接](https://example.com) - 简短描述

## 📊 附录

### 术语表
| 术语 | 定义 |
|------|------|
| 术语1 | 定义1 |
| 术语2 | 定义2 |

### 版本历史
| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2025-06-16 | 初始版本 | 作者名 |

---

> 📝 **文档信息**
> 
> - **创建日期**: 2025-06-16
> - **最后更新**: 2025-06-16
> - **维护者**: 维护者名称
> - **文档版本**: v1.0
> - **适用版本**: 系统版本号
> 
> 🔄 **更新说明**: 使用 `./scripts/doc-maintenance.sh` 进行文档维护
