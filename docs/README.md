# 文档目录

## 📚 核心文档

### 项目概述

- [项目概览](../PROJECT_OVERVIEW.md) - 项目整体概况和任务管理
- [需求说明书](需求说明书.md) - 详细需求和功能规划（包含功能清单）
- [架构文档](architecture.md) - 系统架构设计

### API 和接口

- [API 参考文档](api_reference.md) - 完整的API接口参考 🆕
- [接口文档](接口文档.md) - API 接口详细说明
- [集成指南](integration.md) - 第三方服务集成

### 部署和运维

- [部署指南](deployment.md) - 部署步骤和配置
- [配置管理指南](configuration_guide.md) - 统一配置管理 🆕
- [故障排除指南](troubleshooting.md) - 问题诊断和解决 🆕

## 🛠️ Git 工作流

### Git 文档

- [Git 优化指南](git_optimization_guide.md) - 完整的 Git 配置和工作流程
- [AI 助手 Git 指南](ai_git_guide.md) - AI 助手专用 Git 操作规范

### 最佳实践

- [Cursor 最佳实践](cursor_best_practices.md) - 开发工具使用指南
- [日志和调试](logs_and_debugging.md) - 调试和问题排查

## 🤖 AI 和自动化

### AI 助手文档

- [AI 文档维护指南](ai_documentation_guide.md) - AI助手文档规范
- [任务管理器](task_manager.md) - 任务管理系统

### 对比分析

- [魔术空间 vs OpenManus 对比](魔术空间_vs_OpenManus_对比白皮书.md) - 技术方案对比

## 📋 工作计划

- [后续工作计划](后续工作计划.md) - 项目发展规划

## 📁 归档文档

过时或不再维护的文档存放在 [_archive](_archive/) 目录中。

## 🔧 文档维护

### 维护工具

- **维护脚本**: `scripts/doc-maintenance.sh` - 自动化文档维护
- **文档模板**: `_templates/document_template.md` - 统一文档格式
- **AI助手指南**: `ai_documentation_guide.md` - AI助手文档规范

### 维护记录

- **📋 [文档维护日志](MAINTENANCE_LOG.md)** - 完整的维护历史记录 🔥
- **最近更新**: 2025-06-16 维护脚本更新 + 文档体系重构优化
- **优化效果**: 文档数量-8%，重复内容-80%，完整性+25%，维护自动化+100%

### 维护命令

```bash
# 检查文档状态
./scripts/doc-maintenance.sh check

# 完整维护流程
./scripts/doc-maintenance.sh maintain

# 生成统计报告
./scripts/doc-maintenance.sh stats
```

### 维护规范 (AI助手必读)

#### 🔄 维护原则

1. **减少重复** - 避免内容重复的文档
2. **补充缺失** - 及时补充核心功能文档
3. **保持更新** - 确保文档与代码同步
4. **用户导向** - 以用户需求为导向组织文档

#### 📝 操作要求

**每次维护必须记录**:

- 在 `docs/MAINTENANCE_LOG.md` 中详细记录操作
- 更新 `docs/README.md` 中的最近更新信息
- 重大变更需在 `PROJECT_OVERVIEW.md` 任务管理器中记录

#### 🛠️ 标准流程

```bash
# 1. 执行维护操作
# 2. 记录到维护日志
echo "### $(date +%Y-%m-%d) - 维护描述" >> docs/MAINTENANCE_LOG.md
# 3. 更新文档目录
# 4. 提交变更
git add docs/ && git commit -m "docs: 文档维护 - 具体描述"
```

---

> 📝 **维护说明**: 本目录由文档维护脚本自动生成和更新
>
> 🔄 **最后更新**: 2025-06-16
>
> 🤖 **维护工具**: `scripts/doc-maintenance.sh`
>
> 📋 **维护历史**: 查看 [MAINTENANCE_LOG.md](MAINTENANCE_LOG.md) 了解详细维护记录
