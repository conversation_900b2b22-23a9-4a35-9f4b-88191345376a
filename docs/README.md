# 文档目录

## 📚 核心文档

### 项目概述

- [项目概览](../PROJECT_OVERVIEW.md) - 项目整体概况和任务管理
- [需求说明书](需求说明书.md) - 详细需求和功能规划
- [架构文档](architecture.md) - 系统架构设计

### API 和接口

- [接口文档](接口文档.md) - API 接口详细说明
- [集成指南](integration.md) - 第三方服务集成

### 部署和运维

- [部署指南](deployment.md) - 部署步骤和配置
- [Railway 集成](integration_railway.md) - Railway 平台部署

## 🛠️ Git 工作流

### Git 文档

- [Git 优化指南](git_optimization_guide.md) - 完整的 Git 配置和工作流程
- [Git 快速参考](git_quick_reference.md) - 常用 Git 命令速查表
- [AI 助手 Git 指南](ai_git_guide.md) - AI 助手专用 Git 操作规范

### 最佳实践

- [Cursor 最佳实践](cursor_best_practices.md) - 开发工具使用指南
- [日志和调试](logs_and_debugging.md) - 调试和问题排查

## 🤖 AI 和自动化

### 功能文档

- [功能列表](功能列表.md) - 系统功能清单
- [任务管理器](task_manager.md) - 任务管理系统
- [推荐建议日志](推荐建议日志.md) - 优化建议记录

### 对比分析

- [魔术空间 vs OpenManus 对比](魔术空间_vs_OpenManus_对比白皮书.md) - 技术方案对比

## 📋 工作计划

- [后续工作计划](后续工作计划.md) - 项目发展规划

## 📁 归档文档

过时或不再维护的文档存放在 [_archive](_archive/) 目录中。

---

> 📝 **维护说明**: 本目录由文档维护脚本自动生成和更新
>
> 🔄 **最后更新**: 2025-06-16 15:30:00
>
> 🤖 **维护工具**: `scripts/doc-maintenance.sh`
