# 平台集成现状与建议（补充API Key/配置管理）

## 1. API Key/配置管理
- 所有平台密钥、Token、API Key 建议统一存放于 `config/` 目录下 json 文件或 `.env` 文件。
- 示例：
  - `config/apify.json`：{"APIFY_TOKEN": "your_apify_token"}
  - `config/n8n.json`：{"N8N_API_KEY": "your_n8n_api_key"}
  - `config/mcp_hyperbrowser.json`：{"MCP_API_KEY": "your_mcp_key"}
- 测试用例、自动化脚本应自动读取上述配置，避免硬编码。
- 建议在 `.gitignore` 中忽略密钥文件，防止泄露。

## 2. 现有集成与配置说明
- FlowiseAI、Open Manus、MCP、Apify、n8n 等平台均已生成配置模板。
- 具体配置方法见各平台官方文档及本项目 docs/ 相关说明。

## 3. 常见问题与解决方案
- API Key 无效：请检查密钥是否过期、权限是否足够。
- 本地端口未启动：请确保相关服务已运行。
- 云端部署建议用 Railway/Render/Cloud Run，API Key 配置同理。

> 📝 **最后更新**: 2025-06-15
