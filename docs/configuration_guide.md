# 配置管理指南

> ⚙️ OpenManus Agent 统一配置管理和最佳实践

## 📋 目录

- [配置概述](#配置概述)
- [配置文件结构](#配置文件结构)
- [环境变量管理](#环境变量管理)
- [API密钥管理](#api密钥管理)
- [配置验证](#配置验证)
- [安全最佳实践](#安全最佳实践)

## 🎯 配置概述

### 配置层级
1. **默认配置** - 代码中的默认值
2. **配置文件** - `config/*.toml` 文件
3. **环境变量** - 系统环境变量
4. **运行时参数** - 命令行参数

### 优先级顺序
```
运行时参数 > 环境变量 > 配置文件 > 默认配置
```

## 📁 配置文件结构

### 目录组织
```
config/
├── config.toml              # 主配置文件
├── config.example.toml      # 配置模板
├── secrets.env              # 敏感信息（不入库）
├── apify.json              # Apify配置
├── crawler/                # 爬虫配置
│   ├── mcp_apify.json
│   └── mcp_hyperbrowser.json
├── deployment/             # 部署配置
│   ├── railway.json
│   └── railway_api_test.sh
└── webhook/                # Webhook配置
    └── n8n.json
```

### 主配置文件 (config.toml)

```toml
[app]
name = "OpenManus Agent"
version = "1.0.0"
debug = false
log_level = "INFO"

[server]
host = "0.0.0.0"
port = 8000
workers = 1
timeout = 300

[database]
url = "sqlite:///data/app.db"
pool_size = 10
echo = false

[ai]
provider = "openai"
model = "gpt-4"
temperature = 0.7
max_tokens = 2000

[plugins]
enabled = ["browser_automation", "content_crawler", "api_integrator"]
auto_load = true
sandbox = true

[logging]
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file = "logs/app.log"
max_size = "10MB"
backup_count = 5

[security]
api_key_required = true
rate_limit = 100
cors_origins = ["http://localhost:3000", "http://localhost:3010"]
```

## 🔐 环境变量管理

### secrets.env 文件
```bash
# AI服务配置
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# 第三方服务
APIFY_TOKEN=apify_api_your-token
N8N_API_KEY=your-n8n-api-key
NOTION_API_KEY=your-notion-api-key

# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost/dbname

# 安全配置
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# 外部服务
WEBHOOK_SECRET=your-webhook-secret
RAILWAY_TOKEN=your-railway-token
```

### 环境变量加载
```bash
# 方式1: 手动加载
source config/secrets.env

# 方式2: 自动加载（推荐）
export $(cat config/secrets.env | grep -v '^#' | xargs)

# 方式3: 使用dotenv
pip install python-dotenv
# 在代码中: load_dotenv('config/secrets.env')
```

### 环境变量验证
```bash
# 检查必需的环境变量
required_vars=(
    "OPENAI_API_KEY"
    "APIFY_TOKEN"
    "SECRET_KEY"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "错误: 环境变量 $var 未设置"
        exit 1
    fi
done
```

## 🔑 API密钥管理

### 密钥配置文件

#### config/apify.json
```json
{
  "api_token": "${APIFY_TOKEN}",
  "base_url": "https://api.apify.com/v2",
  "timeout": 30,
  "retry_count": 3,
  "default_memory": 512,
  "default_timeout": 300
}
```

#### config/crawler/mcp_hyperbrowser.json
```json
{
  "api_key": "${HYPERBROWSER_API_KEY}",
  "endpoint": "https://api.hyperbrowser.ai",
  "browser_config": {
    "headless": true,
    "viewport": {
      "width": 1920,
      "height": 1080
    },
    "timeout": 30000
  }
}
```

#### config/webhook/n8n.json
```json
{
  "api_key": "${N8N_API_KEY}",
  "base_url": "http://localhost:5678",
  "webhooks": {
    "task_completion": "/webhook/task-complete",
    "error_notification": "/webhook/error-alert"
  }
}
```

### 密钥轮换策略
```bash
# 1. 生成新密钥
new_key=$(python -c "import secrets; print(secrets.token_urlsafe(32))")

# 2. 更新配置
sed -i "s/old_key_value/$new_key/g" config/secrets.env

# 3. 重新加载配置
source config/secrets.env

# 4. 验证新密钥
curl -H "Authorization: Bearer $new_key" http://localhost:8000/health
```

## ✅ 配置验证

### 配置文件验证脚本
```python
#!/usr/bin/env python3
"""配置验证脚本"""

import os
import toml
import json
from pathlib import Path

def validate_config():
    """验证所有配置文件"""
    config_dir = Path("config")
    
    # 验证主配置文件
    try:
        config = toml.load(config_dir / "config.toml")
        print("✅ config.toml 格式正确")
    except Exception as e:
        print(f"❌ config.toml 格式错误: {e}")
        return False
    
    # 验证JSON配置文件
    json_files = [
        "apify.json",
        "crawler/mcp_apify.json",
        "crawler/mcp_hyperbrowser.json",
        "webhook/n8n.json"
    ]
    
    for json_file in json_files:
        try:
            with open(config_dir / json_file) as f:
                json.load(f)
            print(f"✅ {json_file} 格式正确")
        except Exception as e:
            print(f"❌ {json_file} 格式错误: {e}")
            return False
    
    # 验证环境变量
    required_env_vars = [
        "OPENAI_API_KEY",
        "APIFY_TOKEN",
        "SECRET_KEY"
    ]
    
    for var in required_env_vars:
        if not os.getenv(var):
            print(f"❌ 环境变量 {var} 未设置")
            return False
        print(f"✅ 环境变量 {var} 已设置")
    
    print("🎉 所有配置验证通过")
    return True

if __name__ == "__main__":
    validate_config()
```

### 运行验证
```bash
# 保存验证脚本
cat > scripts/validate_config.py << 'EOF'
# [上面的Python脚本内容]
EOF

# 运行验证
python scripts/validate_config.py
```

## 🔒 安全最佳实践

### 1. 敏感信息保护
```bash
# 确保敏感文件不被提交
echo "config/secrets.env" >> .gitignore
echo "config/*.key" >> .gitignore
echo "config/*_private.json" >> .gitignore

# 设置文件权限
chmod 600 config/secrets.env
chmod 644 config/*.toml
chmod 644 config/*.json
```

### 2. 密钥加密存储
```python
from cryptography.fernet import Fernet
import base64

# 生成加密密钥
key = Fernet.generate_key()
cipher = Fernet(key)

# 加密敏感配置
def encrypt_config(data):
    return cipher.encrypt(data.encode()).decode()

# 解密配置
def decrypt_config(encrypted_data):
    return cipher.decrypt(encrypted_data.encode()).decode()
```

### 3. 配置审计
```bash
# 创建配置变更日志
cat > scripts/config_audit.sh << 'EOF'
#!/bin/bash
echo "$(date): 配置文件检查" >> logs/config_audit.log
find config/ -name "*.toml" -o -name "*.json" | xargs ls -la >> logs/config_audit.log
echo "---" >> logs/config_audit.log
EOF

# 定期运行审计
chmod +x scripts/config_audit.sh
./scripts/config_audit.sh
```

### 4. 备份和恢复
```bash
# 配置备份
backup_config() {
    backup_dir="backups/config_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    cp -r config/ "$backup_dir/"
    echo "配置已备份到: $backup_dir"
}

# 配置恢复
restore_config() {
    if [ -z "$1" ]; then
        echo "用法: restore_config <backup_directory>"
        return 1
    fi
    
    cp -r "$1/config/" .
    echo "配置已从 $1 恢复"
}
```

## 🔧 配置管理工具

### 配置生成脚本
```bash
#!/bin/bash
# scripts/generate_config.sh

echo "🚀 OpenManus Agent 配置生成器"

# 创建配置目录
mkdir -p config/{crawler,deployment,webhook}

# 生成主配置文件
if [ ! -f config/config.toml ]; then
    cp config/config.example.toml config/config.toml
    echo "✅ 已生成 config.toml"
fi

# 生成环境变量文件
if [ ! -f config/secrets.env ]; then
    cat > config/secrets.env << EOF
# AI服务配置
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# 第三方服务
APIFY_TOKEN=your-apify-token-here
N8N_API_KEY=your-n8n-api-key-here

# 安全配置
SECRET_KEY=$(python -c "import secrets; print(secrets.token_urlsafe(32))")
JWT_SECRET=$(python -c "import secrets; print(secrets.token_urlsafe(32))")
EOF
    echo "✅ 已生成 secrets.env"
    echo "⚠️  请编辑 config/secrets.env 填入实际的API密钥"
fi

echo "🎉 配置生成完成"
```

### 配置同步脚本
```bash
#!/bin/bash
# scripts/sync_config.sh

# 从模板同步配置
sync_from_template() {
    if [ -f config/config.example.toml ]; then
        # 备份当前配置
        cp config/config.toml config/config.toml.backup
        
        # 合并新配置
        echo "正在同步配置模板..."
        # 这里可以添加智能合并逻辑
        
        echo "✅ 配置同步完成"
    fi
}

# 验证配置一致性
validate_consistency() {
    python scripts/validate_config.py
}

sync_from_template
validate_consistency
```

## 📚 相关文档

- [部署指南](deployment.md) - 部署时的配置说明
- [API参考](api_reference.md) - API配置相关
- [故障排除](troubleshooting.md) - 配置问题排查

---

> 📝 **文档信息**
> 
> - **创建日期**: 2025-06-16
> - **最后更新**: 2025-06-16
> - **维护者**: 运维团队
> - **文档版本**: v1.0
> 
> 🔄 **更新说明**: 统一配置管理规范，提升安全性和可维护性
