# 文档维护体系复用指南

> 🔄 如何在其他项目中复用这套文档维护体系

## 📋 目录

- [复用概述](#复用概述)
- [完整复用](#完整复用)
- [选择性复用](#选择性复用)
- [定制化配置](#定制化配置)
- [不同项目类型适配](#不同项目类型适配)
- [最佳实践](#最佳实践)

## 🎯 复用概述

### 核心组件
- **文档维护脚本**: `scripts/doc-maintenance.sh`
- **Git工作流脚本**: `scripts/git-workflow.sh`
- **文档模板**: `docs/_templates/`
- **Git配置**: `.githooks/`, `.gitmessage`
- **AI指南**: `docs/ai_documentation_guide.md`

### 适用项目类型
- ✅ 开源项目
- ✅ 企业内部项目
- ✅ 个人项目
- ✅ 多语言项目
- ✅ 前后端分离项目

## 📦 完整复用

### 1. 复制文件结构
```bash
# 创建新项目目录结构
mkdir -p new-project/{scripts,docs/_templates,.githooks}

# 复制核心文件
cp scripts/doc-maintenance.sh new-project/scripts/
cp scripts/git-workflow.sh new-project/scripts/
cp -r docs/_templates/ new-project/docs/
cp -r .githooks/ new-project/
cp .gitmessage new-project/
cp docs/ai_documentation_guide.md new-project/docs/
```

### 2. 配置Git
```bash
cd new-project
chmod +x scripts/*.sh
chmod +x .githooks/*
git config core.hooksPath .githooks
git config commit.template .gitmessage
```

### 3. 初始化文档
```bash
# 生成文档目录
./scripts/doc-maintenance.sh toc

# 检查状态
./scripts/doc-maintenance.sh check
```

## 🎯 选择性复用

### 只需要文档维护
```bash
# 复制文档相关文件
cp scripts/doc-maintenance.sh new-project/scripts/
cp -r docs/_templates/ new-project/docs/
cp docs/ai_documentation_guide.md new-project/docs/

# 修改必需文档列表
vim new-project/scripts/doc-maintenance.sh
# 编辑 required_docs 数组
```

### 只需要Git工作流
```bash
# 复制Git相关文件
cp scripts/git-workflow.sh new-project/scripts/
cp -r .githooks/ new-project/
cp .gitmessage new-project/
cp docs/git_optimization_guide.md new-project/docs/
```

### 只需要AI协作规范
```bash
# 复制AI相关文件
cp docs/ai_documentation_guide.md new-project/docs/
cp docs/ai_git_guide.md new-project/docs/
```

## ⚙️ 定制化配置

### 1. 修改必需文档列表
```bash
# 编辑 scripts/doc-maintenance.sh
required_docs=(
    "README.md"
    "docs/api.md"           # 根据项目调整
    "docs/setup.md"         # 根据项目调整
    "docs/deployment.md"    # 根据项目调整
)
```

### 2. 调整提交信息模板
```bash
# 编辑 .gitmessage
# <类型>(<范围>): <主题>
#
# 类型说明:
# feat:     新功能
# fix:      修复bug
# docs:     文档更新
# style:    代码格式调整
# refactor: 重构代码
# test:     测试相关
# chore:    构建过程或辅助工具的变动

# 范围说明 (根据项目调整):
# api:      API相关
# ui:       用户界面
# core:     核心功能
# config:   配置文件
```

### 3. 自定义文档模板
```bash
# 修改 docs/_templates/document_template.md
# 添加项目特定的章节和格式要求
```

## 🔧 不同项目类型适配

### Python项目
```bash
# 添加Python特定的检查
# 在 doc-maintenance.sh 中添加:
if command -v black &> /dev/null; then
    echo "📝 检查Python代码格式..."
    black --check --diff . || {
        echo "❌ Python代码格式不符合规范"
        exit 1
    }
fi
```

### Node.js项目
```bash
# 添加Node.js特定的检查
if [ -f "package.json" ]; then
    echo "🔧 检查Node.js项目..."
    npm run lint || {
        echo "❌ 代码检查失败"
        exit 1
    }
fi
```

### Go项目
```bash
# 添加Go特定的检查
if command -v gofmt &> /dev/null; then
    echo "📝 检查Go代码格式..."
    gofmt -l . | grep -v vendor/ | head -10
fi
```

### 多语言项目
```bash
# 添加多语言文档支持
required_docs=(
    "README.md"
    "README_zh.md"
    "README_ja.md"
    "docs/api.md"
    "docs/api_zh.md"
)
```

## 📚 最佳实践

### 1. 项目初始化清单
- [ ] 复制核心脚本文件
- [ ] 配置Git hooks和模板
- [ ] 修改必需文档列表
- [ ] 调整提交信息模板
- [ ] 创建项目特定的文档模板
- [ ] 运行初始检查

### 2. 团队协作
```bash
# 在项目README中添加使用说明
## 📚 文档维护

本项目使用自动化文档维护工具：

```bash
# 检查文档状态
./scripts/doc-maintenance.sh check

# 完整维护流程
./scripts/doc-maintenance.sh maintain

# Git工作流
./scripts/git-workflow.sh workflow
```

### 3. CI/CD集成
```yaml
# .github/workflows/docs.yml
name: Documentation Check
on: [push, pull_request]
jobs:
  docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Check documentation
        run: |
          chmod +x scripts/doc-maintenance.sh
          ./scripts/doc-maintenance.sh check
          ./scripts/doc-maintenance.sh validate-links
```

### 4. 定期维护
```bash
# 设置定期任务
# 每周运行完整维护
0 9 * * 1 cd /path/to/project && ./scripts/doc-maintenance.sh maintain
```

## 🔄 版本管理

### 创建可复用的模板仓库
```bash
# 创建模板仓库
git clone https://github.com/your-username/doc-maintenance-template.git
cd doc-maintenance-template

# 只保留核心文件
git filter-branch --subdirectory-filter scripts HEAD
git filter-branch --subdirectory-filter docs/_templates HEAD
git filter-branch --subdirectory-filter .githooks HEAD

# 推送模板仓库
git remote set-url origin https://github.com/your-username/doc-maintenance-template.git
git push -u origin main
```

### 使用模板创建新项目
```bash
# 使用GitHub模板功能
gh repo create new-project --template your-username/doc-maintenance-template

# 或手动克隆
git clone https://github.com/your-username/doc-maintenance-template.git new-project
cd new-project
rm -rf .git
git init
```

## 📊 复用效果评估

### 成功指标
- ✅ 文档覆盖率 > 90%
- ✅ 链接有效性 > 95%
- ✅ 文档更新及时性
- ✅ 团队采用率 > 80%

### 持续改进
- 收集使用反馈
- 优化脚本性能
- 增加新功能
- 更新最佳实践

---

> 📝 **维护说明**: 本指南会随着工具的发展持续更新
> 
> 🔄 **最后更新**: 2025-06-16
> 
> 🤝 **贡献**: 欢迎提交改进建议和使用案例
